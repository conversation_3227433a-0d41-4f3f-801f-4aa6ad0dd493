<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="All Test Suite" parallel="methods" skipfailedinvocationcounts="true" thread-count="1" data-provider-thread-count="10">
    <listeners>
        <listener class-name="io.qameta.allure.testng.AllureTestNg"/>
        <!--        <listener class-name="helpers.factories.AllureLogListener"/>-->
    </listeners>
    <test name="Food Aggregator Tests">
        <classes>
            <class name="customerApp.androidNative.foodAggregators.homePage.HomeBusinessCategoriesFiltersTests">
                <methods>
                    <include name="TC_729VerifyAllExistingCategoriesAreLinkedToRestaurantsInSpecificLocation"/>
                    <include name="TC_733VerifyCategoryFilterSelectionPersistsAfterUserOpenRestaurantPageAndReturns"/>
                    <include name="TC_736VerifyClickingOnSelectedFilterAgainWillResetTheCategoryFilter"/>
                </methods>
            </class>
            <class name="customerApp.iosNative.foodAggregators.homePage.HomeBusinessCategoriesFiltersTests">
                <methods>
                    <include name="TC_729VerifyAllExistingCategoriesAreLinkedToRestaurantsInSpecificLocation"/>
                    <include name="TC_733VerifyCategoryFilterSelectionPersistsAfterUserOpenRestaurantPageAndReturns"/>
                    <include name="TC_736VerifyClickingOnSelectedFilterAgainWillResetTheCategoryFilter"/>
                </methods>
            </class>
            <class name="customerApp.androidNative.foodAggregators.homePage.HomeRestaurantsLandingTests">
                <methods>
                    <include name="TC_655VerifyRestaurantStatusDisplayOnRestaurantCard"/>
                    <include name="TC_655VerifyRatingVisibleWithHighReviewCount"/>
                    <include name="TC_654VerifyRatingHiddenWithLowReviewCount"/>
                    <include name="TC_651VerifyDisplayOfRestaurantDeliveryTimeOnCard"/>
                </methods>
            </class>
            <class name="customerApp.iosNative.foodAggregators.homePage.HomeRestaurantsLandingTests">
                <methods>
                    <include name="TC_655VerifyRestaurantStatusDisplayOnRestaurantCard"/>
                    <include name="TC_655VerifyRatingVisibleWithHighReviewCount"/>
                    <include name="TC_654VerifyRatingHiddenWithLowReviewCount"/>
                    <include name="TC_651VerifyDisplayOfRestaurantDeliveryTimeOnCard"/>
                </methods>
            </class>
            <class name="customerApp.androidNative.foodAggregators.CartScreenTests">
                <methods>
                    <include name="validateRestaurantsCartFunctionality"/>
                </methods>
            </class>
            <class name="customerApp.iosNative.foodAggregators.CartScreenTests">
                <methods>
                    <include name="validateRestaurantsCartFunctionality"/>
                </methods>
            </class>
            <class name="customerApp.androidNative.foodAggregators.PlaceOrderTests">
                <methods>
                    <include name="validateOrderIsPlacedSuccessfully"/>
                </methods>
            </class>
            <class name="customerApp.iosNative.foodAggregators.PlaceOrderTests">
                <methods>
                    <include name="validateOrderIsPlacedSuccessfully"/>
                </methods>
            </class>
        </classes>
    </test>

</suite>