package Inventory;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import models.Batch;
import models.Product;
import models.StockBuckets;
import org.json.JSONArray;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Map;

public class InventoryAPIsTests extends BaseTest {
    @Test
    @Tags({@Tag("api"), @Tag("controlRoom")})
    public void validateDeductTriggersAdjustmentApprovals() {

        defaultTestData.get().setDeductResult(
                inventoryApiClient.get().deductStockAndTriggerApproval(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(), "Missing/Theft"));

        Assert.assertNotNull(defaultTestData.get().getDeductResult(),
                "Deduction failed after retrying all batches. Check logs for details.");

        // API success assertion
        jsonPath.set(new JsonPath(((Response)defaultTestData.get().getDeductResult().get("response")).then().extract().asString()));
        Assert.assertEquals(jsonPath.get().getString("success"), "true");

        // Stock bucket assertions
        Assert.assertEquals(
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockAfter")).getNotSellable(),
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockBefore")).getNotSellable() + Math.abs((int) defaultTestData.get().getDeductResult().get("delta")),
                0.01);
        Assert.assertEquals(
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockAfter")).getOnApp(),
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockBefore")).getOnApp() - Math.abs((int) defaultTestData.get().getDeductResult().get("delta")),
                0.01);

        // Manual approval check
        Assert.assertTrue(
                inventoryApiClient.get().hasMatchingAdjustment(
                        inventoryApiClient.get().getAllManualAdjustments(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(),
                                ((Product) defaultTestData.get().getDeductResult().get("product")).getMysqlId()), ((Batch) defaultTestData.get().getDeductResult().get("batch")).getId(),
                        (int) defaultTestData.get().getDeductResult().get("delta"), "Missing/Theft"),
                String.format("Expected manual adjustment not found. ProductID: %d, BatchID: %d, Delta: %d",
                        ((Product) defaultTestData.get().getDeductResult().get("product")).getMysqlId(), ((Batch) defaultTestData.get().getDeductResult().get("batch")).getId(), (int) defaultTestData.get().getDeductResult().get("delta"))
        );
    }
}
