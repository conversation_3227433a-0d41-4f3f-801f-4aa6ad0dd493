package planningCenter;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class PlanningCenterTests extends BaseTest {
    @Test
    @Tags({@Tag("api"), @Tag("planning-center")})
    public void createSchedulerEverydayDeliverySameDayWithFuturePlacingTime(){
        defaultTestData.get().getPlanningCenter().setSchedulerId(planningCenterApiClient.get().createSchedulers(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestWarehouse()
                , defaultTestData.get().getPlanningCenter()
                , "Below minimum to safety"
                , true
                ,"Same Day"
                , true, true).getSchedulerId());

        planningCenterApiClient.get().getSchedulerDetails(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getPlanningCenter());

        Assert.assertEquals(defaultTestData.get().getPlanningCenter().getStatusId(),2);

        planningCenterApiClient.get().generateAndResetStatus(defaultTestData.get().getAdminUser());

        planningCenterApiClient.get().processAndCalculate(defaultTestData.get().getAdminUser());

        planningCenterApiClient.get().syncToOdoo(defaultTestData.get().getAdminUser());

        planningCenterApiClient.get().getSchedulerDetails(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getPlanningCenter());

        defaultTestData.get().getPlanningCenter().setStatusId(
                planningCenterApiClient.get().getInternalOrders(defaultTestData.get().getAdminUser()
                        ,defaultTestData.get().getPlanningCenter().getSchedulerId()).getStatusId());

        Assert.assertNotEquals(defaultTestData.get().getPlanningCenter().getStatusId(), 2);

        planningCenterApiClient.get().deleteScheduler(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getPlanningCenter());
    }

    @Test
    @Tags({@Tag("api"), @Tag("planning-center")})
    public void createSchedulerEverydayDeliverySameDayWithPastPlacingTime(){
        defaultTestData.get().getPlanningCenter().setSchedulerId(
                planningCenterApiClient.get().createSchedulers(defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestWarehouse()
                , defaultTestData.get().getPlanningCenter()
                , "Below minimum to safety"
                , true
                ,"Same Day"
                , true, false)
                        .getSchedulerId());

        planningCenterApiClient.get().getSchedulerDetails(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getPlanningCenter());

        Assert.assertEquals(defaultTestData.get().getPlanningCenter().getStatusId(),2);

        planningCenterApiClient.get().getInternalOrders(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getPlanningCenter().getSchedulerId());

        Assert.assertNull(defaultTestData.get().getPlanningCenter().getPayload());

        planningCenterApiClient.get().deleteScheduler(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getPlanningCenter());
    }

    @Test(groups = {"ops"})
    @Tags({@Tag("api"), @Tag("planning-center")})
    public void createSchedulerCustomDeliverySameDayWithFuturePlacingTime(){
        defaultTestData.get().getPlanningCenter().setSchedulerId(planningCenterApiClient.get().createSchedulers(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestWarehouse()
                , defaultTestData.get().getPlanningCenter()
                , "Below minimum to safety"
                , true
                ,"Same Day"
                , false, true).getSchedulerId());

        planningCenterApiClient.get().getSchedulerDetails(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getPlanningCenter());

        Assert.assertEquals(defaultTestData.get().getPlanningCenter().getStatusId(),1);

        planningCenterApiClient.get().deleteScheduler(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getPlanningCenter());
    }
}
