package fleetApp.android;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class LoginFlowTests extends BaseTest {

    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void validateLoggingInWithValidCredentialsIsWorking() {

        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());

        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();

        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),defaultTestData.get().getDaUser().getLocalPhoneNumber());
        Assert.assertTrue(androidSplashScreen.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnEnabled());

        androidSplashScreen.get().setPassword(defaultTestData.get().getDaUser().getBypassScriptPassword());
        androidSplashScreen.get().pressPasswordLoginBtn();
    }

    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void loginWithWrongNumber() {
        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());
        androidSplashScreen.get().setMobileNumber("12345678912");
        androidSplashScreen.get().pressConfirmBtn();
        Assert.assertEquals(androidSplashScreen.get().getWrongNumberTxt(),"يجب إدخال رقم صحيح");
    }
    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void loginWithWrongPassword() {
        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());
        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),defaultTestData.get().getDaUser().getLocalPhoneNumber());
        Assert.assertTrue(androidSplashScreen.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnEnabled());
        androidSplashScreen.get().setPassword(defaultTestData.get().getRandomTestUser().getEmailPassword());
        androidSplashScreen.get().pressPasswordLoginBtn();
        Assert.assertEquals(androidSplashScreen.get().getWrongPasswordTxt(),"كلمة المرور غير صحيحة");
    }
    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void CheckEmptyStateScreen() {
        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());
        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnEnabled());
        androidSplashScreen.get().setPassword(defaultTestData.get().getDaUser().getBypassScriptPassword());
        androidSplashScreen.get().pressPasswordLoginBtn();
        Assert.assertEquals(androidSplashScreen.get().getEmptyScreenTxt(),"ليس لديك شحنات لتوصيلها");
        Assert.assertEquals(androidSplashScreen.get().getEmptySecondScreenTxt(),"الشحنات المطلوب منك توصيلها سوف تظهر هنا");
    }

    @Test
    @Tags({@Tag("android"), @Tag("fletApp")})
    public void loggingInWithUnauthorizedNumber() {
        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());
        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getRandomTestUser().getPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();
        Assert.assertEquals(androidSplashScreen.get().getUnauthorizedNumberTxt(),"هذا الرقم غير مصرح له بالدخول");
    }
}
