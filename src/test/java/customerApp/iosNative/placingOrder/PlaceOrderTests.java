package customerApp.iosNative.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.util.stream.Collectors;

@Test(groups = {"place-order"})
public class PlaceOrderTests extends BaseTest {
    @Test(groups = {"place-order", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void PlacingAndTrackingInstantOrder() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        // instant is displayed
        Assert.assertTrue(iosNativeHomeScreen.get().instantDisplay());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(iosNativeCategoryDetailsScreen.get().isPageDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getNowProductWithPositiveStock().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMongoId());

        iosNativeCategoryDetailsScreen.get().pressCartBtn();

        //click on goto check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());

        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        // ScheduleBtn is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isScheduleBtnDisplayed());

        // click on place order
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");

        iosNativeOrderSuccessScreen.get().pressBackBtn();

        Assert.assertTrue(iosNativeHomeScreen.get().placeOrderMiniTrackingIsDisplayed());

        iosNativeHomeScreen.get().pressExpandMiniTracking();

        iosNativeHomeScreen.get().pressViewOrderDetails();

        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());
    }

    @Test(groups = {"place-order", "regression", "deep-regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void PlaceOrderInBusyFP() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                0);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        // instant is displayed
        Assert.assertTrue(iosNativeHomeScreen.get().instantDisplay());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(iosNativeCategoryDetailsScreen.get().isPageDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getNowProductWithPositiveStock().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMongoId());

        iosNativeCategoryDetailsScreen.get().pressCartBtn();

        //click on goto check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());

        //fp busy
        iosNativeCartScreen.get().isBusyMessageDisplayed();

        //click on goto check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());

        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        //busy message appear
        Assert.assertTrue(iosNativeCartScreen.get().isBusyMessageDisplayed());
    }

    @Test(groups = {"place-order", "regression", "deep-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping")})
    public void FPBusyWhileClickingPlaceOrderAndAppearModalWithVeryNextAvailableTimeslot() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //login
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        // instant is displayed
        Assert.assertTrue(iosNativeHomeScreen.get().instantDisplay());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(iosNativeCategoryDetailsScreen.get().isPageDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getNowProductWithPositiveStock().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMongoId());

        iosNativeCategoryDetailsScreen.get().pressCartBtn();

        //click on goto check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());

        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        // change availability
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());

        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");

        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(2), "100");

        // click on place order
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        //Delivery Time Updated PopUp appear
        Assert.assertTrue(iosNativeCheckoutScreen.get().DeliveryTimeUpdatedPopUpIsDisplayed());

        //click on place order
        iosNativeCheckoutScreen.get().PressDeliveryTimeUpdatedPopUpPlaceOrder();

        //OrderSuccessScreen display
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());
    }

    @Test(groups = {"place-order", "regression", "deep-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping")})
    public void PlacingAndTrackingScheduledOrder() {
        // instant slot is busy and set Capacity after 2h, 3h and 4h
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        webDeliveryCapacityManagementPage.get().goToPage();

        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());

        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");

        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(2), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(3), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(4), "100");

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //login
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(iosNativeCategoryDetailsScreen.get().isPageDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getNowProductWithPositiveStock().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMongoId());

        iosNativeCategoryDetailsScreen.get().pressCartBtn();

        //click on goto check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        iosNativeCheckoutScreen.get().PressScheduleBtn();

        Assert.assertTrue(iosNativeCheckoutScreen.get().ScheduleAnotherTimeSheetIsDisplayed());
        Assert.assertTrue(iosNativeCheckoutScreen.get().InstantInScheduleSheetIsDisplayed());
        Assert.assertTrue(iosNativeCheckoutScreen.get().FirstFullyBookedScheduleSlotIsDisplayed());

        iosNativeCheckoutScreen.get().PressFirstAvailableScheduleSlot();
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        //Delivery Time Updated PopUp appear
        Assert.assertTrue(iosNativeCheckoutScreen.get().DeliveryTimeUpdatedPopUpIsDisplayed());

        //click on place order
        iosNativeCheckoutScreen.get().PressDeliveryTimeUpdatedPopUpPlaceOrder();

        //OrderSuccessScreen display
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        iosNativeOrderSuccessScreen.get().pressBackBtn();

        Assert.assertTrue(iosNativeHomeScreen.get().placeOrderMiniTrackingIsDisplayed());
    }

    @Test(groups = {"place-order", "regression", "deep-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping")})
    public void ForcedSchedulingConfirmationMessageWhenNextTimeslotGreaterThan3HFromNow(){
        // instant slot is busy and set Capacity after 6h
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        webDeliveryCapacityManagementPage.get().goToPage();

        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());

        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");

        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(6), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(7), "100");

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //login
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(iosNativeCategoryDetailsScreen.get().isPageDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getNowProductWithPositiveStock().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMongoId());

        iosNativeCategoryDetailsScreen.get().pressCartBtn();
        //click on goto check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        Assert.assertTrue(iosNativeCheckoutScreen.get().ClosestSlotDueToHighDemandMsgIsDisplayed());

        Assert.assertTrue(iosNativeCheckoutScreen.get().BookThisSlotBtnIsDisplayed());

        Assert.assertTrue(iosNativeCheckoutScreen.get().PickAnotherTimeBtnIsDisplayed());

        iosNativeCheckoutScreen.get().PressBookThisSlotBtn();

        Assert.assertTrue(iosNativeCheckoutScreen.get().DeliverAtTheEarliestToggleIsDisplayed());

        iosNativeCheckoutScreen.get().PressDeliverAtTheEarliestToggleBtn();

        Assert.assertTrue(iosNativeCheckoutScreen.get().DeliverAtTheEarliestPopupSheetIsDisplayed());

        iosNativeCheckoutScreen.get().PressGotItBtn();

        iosNativeCheckoutScreen.get().PressDeliverAtTheEarliestToggleBtn();

        iosNativeCheckoutScreen.get().PressDeliverAtTheEarliestToggleBtn();

        Assert.assertTrue(iosNativeCheckoutScreen.get().DeliverAtTheEarliestPopupSheetIsDisplayed());

        iosNativeCheckoutScreen.get().PressGotItBtn();

        iosNativeCheckoutScreen.get().PressDeliverAtTheEarliestToggleBtn();

        iosNativeCheckoutScreen.get().PressDeliverAtTheEarliestToggleBtn();

        Assert.assertTrue(iosNativeCheckoutScreen.get().DeliverAtTheEarliestPopupSheetIsDisplayed());

        iosNativeCheckoutScreen.get().PressGotItBtn();

        iosNativeCheckoutScreen.get().PressDeliverAtTheEarliestToggleBtn();

        iosNativeCheckoutScreen.get().PressDeliverAtTheEarliestToggleBtn();

        Assert.assertFalse(iosNativeCheckoutScreen.get().DeliverAtTheEarliestPopupSheetIsDisplayed());

        // click on place order
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        //OrderSuccessScreen display
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());
    }

    @Test(groups = {"place-order", "regression", "deep-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping")})
    public void ForcedSchedulingConfirmationMessageWhenNextTimeslotGreaterThan3HFromNowMoreThanOneAvailableSlots(){
        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //login
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(iosNativeCategoryDetailsScreen.get().isPageDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getNowProductWithPositiveStock().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMongoId());

        iosNativeCategoryDetailsScreen.get().pressCartBtn();

        // instant slot is busy and set Capacity after 6h,7h, 8h and 9h
        webLoginPage.get().goToLoginPage();

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        webDeliveryCapacityManagementPage.get().goToPage();

        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());

        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");

        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(6), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(7), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(8), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(9), "100");

        //click on goto check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());

        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        Assert.assertTrue(iosNativeCheckoutScreen.get().ClosestSlotDueToHighDemandMsgIsDisplayed());

        Assert.assertTrue(iosNativeCheckoutScreen.get().BookThisSlotBtnIsDisplayed());

        Assert.assertTrue(iosNativeCheckoutScreen.get().PickAnotherTimeBtnIsDisplayed());

        iosNativeCheckoutScreen.get().PressPickAnotherTimeBtn();

        Assert.assertTrue(iosNativeCheckoutScreen.get().ScheduleAnotherTimeSheetIsDisplayed());

        iosNativeCheckoutScreen.get().PressFirstAvailableScheduleSlot();

        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        //click on place order
        iosNativeCheckoutScreen.get().PressDeliveryTimeUpdatedPopUpPlaceOrder();

        //OrderSuccessScreen display
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());
    }

    @Test(groups = {"place-order", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void validateCancelOrderFunctionalityIsWorkingCorrectly() {
        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // place order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));
        //login
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Home Screen
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        Assert.assertTrue(iosNativeHomeScreen.get().placeOrderMiniTrackingIsDisplayed());
        iosNativeHomeScreen.get().pressExpandMiniTracking();
        iosNativeHomeScreen.get().pressViewOrderDetails();

        //ToDo: Fill in scroll Btn method

        // Cancel order
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isCancelBtnDisplayed());
        iosNativeOrderSuccessScreen.get().pressCancelBtn();

    }
}
