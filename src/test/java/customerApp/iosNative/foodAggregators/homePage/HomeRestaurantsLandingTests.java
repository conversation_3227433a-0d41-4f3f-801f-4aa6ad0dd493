package customerApp.iosNative.foodAggregators.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import models.Restaurant;
import models.ValidationResults;
import org.openqa.selenium.WebElement;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Test class for Restaurant Home Landing functionality
 */

public class HomeRestaurantsLandingTests extends BaseTest {

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"),  @Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_655VerifyRestaurantStatusDisplayOnRestaurantCard() {
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , "id=" + iosNativeFoodAggregatorHomeScreen.get().getCategoryFilterIdSelector(String.valueOf(defaultTestData.get()
                        .getFoodAggregatorTestSession().getRestaurantsList().getLast().getYeloId())));

        // Compare restaurant operating status and make sure closed tag is shown in the app
        String apiRestaurantStatus = defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().getLast().getOperatingStatus();

        Assert.assertEquals(
                apiRestaurantStatus.toLowerCase(),
                iosNativeFoodAggregatorHomeScreen.get().verifyRestaurantStatus(apiRestaurantStatus, String.valueOf(defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().getLast().getYeloId())));
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"),  @Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_655VerifyRatingVisibleWithHighReviewCount() {
                defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode()
        );
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        List<Restaurant> allRestaurants = defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList();
        List<Restaurant> highReviewRestaurants = allRestaurants.stream()
                .filter(r -> r.getReviewCount() >= 50)
                .collect(Collectors.toList());

        ValidationResults finalResults = iosNativeTestsExecutionHelper.get().progressiveScrollAndVerifyAllRestaurants(
                iosDriver.get(),
                iosNativeFoodAggregatorHomeScreen.get(),
                highReviewRestaurants,
                restaurantId -> iosNativeFoodAggregatorHomeScreen.get().checkIfRatingIsDisplayed(restaurantId),
                "High Review Rating Visibility Verification"
        );
        Assert.assertTrue(
                finalResults.isResult(),
                "❌ Rating visibility verification failed for restaurants with reviewCount >= 50. Issues found:\n" +
                        String.join("\n", finalResults.getValidationResults())
        );
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"),  @Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_654VerifyRatingHiddenWithLowReviewCount() {
                defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode()
        );

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        List<Restaurant> allRestaurants = defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList();
        List<Restaurant> lowReviewRestaurants = allRestaurants.stream()
                .filter(r -> r.getReviewCount() < 50)
                .collect(Collectors.toList());

        ValidationResults finalResults = iosNativeTestsExecutionHelper.get().progressiveScrollAndVerifyAllRestaurants(
                iosDriver.get(),
                iosNativeFoodAggregatorHomeScreen.get(),
                lowReviewRestaurants,
                restaurantId -> !iosNativeFoodAggregatorHomeScreen.get().checkIfRatingIsDisplayed(restaurantId),
                "Low Review Rating Hidden Verification"
        );

        Assert.assertTrue(
                finalResults.isResult(),
                "❌ Rating verification failed for restaurants with reviewCount < 50. Issues found:\n" +
                        String.join("\n", finalResults.getValidationResults())
        );
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"),  @Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_648VerifyDisplayOfRestaurantNameOnCard()
    {
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        List<WebElement> visibleCards = iosNativeFoodAggregatorHomeScreen.get().getAllVisibleRestaurantCards();
        Assert.assertFalse(visibleCards.isEmpty(), "No restaurant cards found on the screen.");

        String cardId = visibleCards.get(0).getAttribute("name");
        String numericId = cardId.replace("homeRestaurantCard[", "").replace("]", "");
        String getRestaurantNameFromUI = iosNativeFoodAggregatorHomeScreen.get().getRestaurantName(numericId);
        String getRestaurantNameFromAPI = defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().get(0).getName();
        Assert.assertEquals(getRestaurantNameFromUI, getRestaurantNameFromAPI, "Mismatch in restaurant name between API and UI");

    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"),  @Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_651VerifyDisplayOfRestaurantDeliveryTimeOnCard()
    {
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        List<WebElement> visibleCards = iosNativeFoodAggregatorHomeScreen.get().getAllVisibleRestaurantCards();
        Assert.assertFalse(visibleCards.isEmpty(), "No restaurant cards found on the screen.");

        String cardId = visibleCards.get(0).getAttribute("name");
        String numericId = cardId.replace("homeRestaurantCard[", "").replace("]", "");
        String deliveryTimeFromUI = iosNativeFoodAggregatorHomeScreen.get().getRestaurantDeliveryTime(numericId).replaceAll("[^0-9]", "");
        String deliveryTimeFromAPI = String.valueOf(defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().get(0).getDeliveryTime());
        Assert.assertEquals(deliveryTimeFromUI, deliveryTimeFromAPI, "Mismatch in restaurant delivery time between API and UI");

    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"),  @Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_652VerifyDisplayOfRestaurantDeliveryFeesOnCard()
    {
                defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode()
        );

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        List<Restaurant> allRestaurants = defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList();
        ValidationResults finalResults = iosNativeTestsExecutionHelper.get().progressiveScrollAndVerifyAllRestaurants(
                iosDriver.get(),
                iosNativeFoodAggregatorHomeScreen.get(),
                allRestaurants,
                restaurantId -> iosNativeFoodAggregatorHomeScreen.get().waitForDeliveryFeeToBeVisible(iosDriver.get(),restaurantId),
                "Delivery Fee Visibility Verification"
        );

        Assert.assertTrue(
                finalResults.isResult(),
                "❌ Delivery fee visibility verification failed. Issues found:\n" +
                        String.join("\n", finalResults.getValidationResults())
        );
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_CheckRestaurantsListReturningWithLoggedOutUser()
    {
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed("egypt");
        iosNativeLandingScreen.get().pressExploreBtn();
        // Verify home screen and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        //Assert the Restaurant page is shown for the logged out user
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());
    }

    @Test
    @Tags({@Tag("food-aggregator"),  @Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_539_CheckAppWillBehaveNormallyAfterSendingToBG ()
    {
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Validate that home screen is opened
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        //click on Restaurants entry point
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        //Assert Restaurants landing screen are shown
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed(),"Restaurants landing screen is not Displayed yet!");

        // Send the app to the Background
        iosDriver.get().runAppInBackground(Duration.ofSeconds(6));
        iosDriver.get().activateApp("com.breadfast.testing");
        //Assert again the app is Up and the restaurants landing screen is working
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed(),"Restaurants landing screen is not Displayed yet!");
    }

    @Test
    @Tags({@Tag("food-aggregator"),  @Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_635_CheckTheUserCanAccessAggregationCart ()
    {
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Validate that home screen is opened
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        //click on Restaurants entry point
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCartDisplayed(),"Cart Icon is not Displayed in Aggregation landing screen");
    }
}
