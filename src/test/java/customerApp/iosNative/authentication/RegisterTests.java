package customerApp.iosNative.authentication;

import base.BaseTest;
import helpers.factories.dataFactories.OtpFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class RegisterTests extends BaseTest {
    @Test
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void registerAndLoginWithLocalValidPhoneNumber(){
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativePhoneNumberScreen.get().enterPhoneNumberAndPresNext(
                defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "register", defaultTestData.get().getRandomTestUser()));

        iosNativeOtpVerificationScreen.get().enterOtp(defaultTestData.get().getRandomTestUser().getOtp());

        Assert.assertTrue(iosNativeCreateAccountScreen.get().isPageDisplayed());
        iosNativeCreateAccountScreen.get().fillInAccountInformationForm(defaultTestData.get());
        iosNativeCreateAccountScreen.get().pressSubmitBtn();

        Assert.assertTrue(iosNativeRegisterSuccessScreen.get().isPageDisplayed());
        iosNativeRegisterSuccessScreen.get().pressDoneBtn();

        iosNativeHomeScreen.get().pressMoreBtn();
        Assert.assertTrue(iosNativeMoreScreen.get().getDisplayedUserFullName()
                .equalsIgnoreCase(defaultTestData.get().getRandomTestUser().getFullName()));

        iosNativeHomeScreen.get().pressHomeBtn();

        Assert.assertNotNull(switcherApiClient.get().searchForUser(defaultTestData.get().getRandomTestUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser()).getId());

        iosNativeTestsExecutionHelper.get().logoutAndGoToPhoneInputScreen(iosDriver.get(),
                iosNativeHomeScreen.get(),
                iosNativeMoreScreen.get());

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
    }

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void validateRegexOfRegisterForm(){
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativePhoneNumberScreen.get().enterPhoneNumberAndPresNext(
                defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "register", defaultTestData.get().getRandomTestUser()));

        iosNativeOtpVerificationScreen.get().enterOtp(defaultTestData.get().getRandomTestUser().getOtp());

        Assert.assertTrue(iosNativeCreateAccountScreen.get().isPageDisplayed());

        iosNativeCreateAccountScreen.get().pressSubmitBtn();
        Assert.assertTrue(iosNativeCreateAccountScreen.get().isEmptyFirstNameErrorMsgDisplayed());

        iosNativeCreateAccountScreen.get().enterFirstName("1123abcde@@");
        iosNativeCreateAccountScreen.get().pressSubmitBtn();
        Assert.assertTrue(iosNativeCreateAccountScreen.get().isInvalidFirstNameErrorMsgDisplayed());

        iosNativeCreateAccountScreen.get().clearTxtField("firstname");
        iosNativeCreateAccountScreen.get().enterFirstName(defaultTestData.get().getRandomTestUser().getFirstName());

        iosNativeCreateAccountScreen.get().enterLastName("");
        iosNativeCreateAccountScreen.get().pressSubmitBtn();
        Assert.assertTrue(iosNativeCreateAccountScreen.get().isEmptyLastNameErrorMsgDisplayed());
        iosNativeCreateAccountScreen.get().enterLastName("1123abcde@@");
        iosNativeCreateAccountScreen.get().pressSubmitBtn();
        Assert.assertTrue(iosNativeCreateAccountScreen.get().isInvalidLastNameErrorMsgDisplayed());
        iosNativeCreateAccountScreen.get().clearTxtField("lastname");
        iosNativeCreateAccountScreen.get().enterLastName(defaultTestData.get().getRandomTestUser().getLastName());

        Assert.assertTrue(iosNativeCreateAccountScreen.get().isSubmitBtnEnabled());

        iosNativeCreateAccountScreen.get().enterEmail("1212323abcdef@");
        iosNativeCreateAccountScreen.get().pressSubmitBtn();
        Assert.assertTrue(iosNativeCreateAccountScreen.get().isInvalidEmailErrorMsgDisplayed());
        iosNativeCreateAccountScreen.get().clearTxtField("email");
        iosNativeCreateAccountScreen.get().enterEmail(defaultTestData.get().getRandomTestUser().getEmailAddress());
        iosNativeCreateAccountScreen.get().pressSubmitBtn();

        Assert.assertTrue(iosNativeRegisterSuccessScreen.get().isPageDisplayed());
    }
}
