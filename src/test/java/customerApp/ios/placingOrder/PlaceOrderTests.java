package customerApp.ios.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class PlaceOrderTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingOrderDeliveryTimeIsCorrectlyDisplayedInSuccessScreen(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);

        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        defaultTestData.get().getTestOrder().setExpectedDeliveryTime(
                testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy") + " "
                        + (iosCheckoutScreen.get().getInstantDeliveryExpectedTime().contains("before")
                        ? iosCheckoutScreen.get().getInstantDeliveryExpectedTime()
                        : "in " + iosCheckoutScreen.get().getInstantDeliveryExpectedTime().toUpperCase()));

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());

        // Check if the balance toggle is ON
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to COD
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // Implement assertion logic to ensure the total is = 0 when the Balance is selected as payment method
        iosCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosOrderSuccessScreen.get().getScrollableContentContainer()
                , iosOrderSuccessScreen.get().getDeliveryDateTimeContainerNameSelector());

        defaultTestData.get().getTestOrder().setActualDeliveryTime(iosOrderSuccessScreen.get().getDeliveryDateValue());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingOrderWithCODIsWorkingCorrectly(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());
        iosHomeScreen.get().isHomePageDisplayed();

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        defaultTestData.get().getTestOrder().setExpectedDeliveryTime(
                testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy") + " "
                        + (iosCheckoutScreen.get().getInstantDeliveryExpectedTime().contains("before")
                        ? iosCheckoutScreen.get().getInstantDeliveryExpectedTime()
                        : "in " + iosCheckoutScreen.get().getInstantDeliveryExpectedTime()));

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());
        // Check if the balance toggle is ON
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");
        // Update the Payment method to COD
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        // Implement assertion logic to ensure the total is = 0 when the Balance is selected as payment method
        iosCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed()
                , "Order success screen isn't displayed");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingOrderWithCreditCardIsWorkingCorrectly(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        defaultTestData.get().getTestOrder().setExpectedDeliveryTime(
                testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy") + " "
                        + (iosCheckoutScreen.get().getInstantDeliveryExpectedTime().contains("before")
                        ? iosCheckoutScreen.get().getInstantDeliveryExpectedTime()
                        : "in " + iosCheckoutScreen.get().getInstantDeliveryExpectedTime()));

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());
        // Check if the balance toggle is ON
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");
        // Update the Payment method to COD
        iosCheckoutScreen.get().pressCreditCardPaymentOption();
        Assert.assertTrue(iosCardSelectionModal.get().isModalDisplayed()
                , "Credit Card selection Modal isn't displayed");
        iosCardSelectionModal.get().pressAddNewCardOption();

        // Implement assertion logic to ensure the total is = 0 when the Balance is selected as payment method
        iosCheckoutScreen.get().pressPlaceOrderBtn();

        //Add card info and press add card
        iosAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed()
                , "Order success screen isn't displayed");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateThatOrderExistsInOrdersListAfterCreation(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        defaultTestData.get().getTestOrder().setExpectedDeliveryTime(
                testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy") + " "
                        + (iosCheckoutScreen.get().getInstantDeliveryExpectedTime().contains("before")
                        ? iosCheckoutScreen.get().getInstantDeliveryExpectedTime()
                        : "in " + iosCheckoutScreen.get().getInstantDeliveryExpectedTime()));

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());

        // Check if the balance toggle is ON
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to COD
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // Implement assertion logic to ensure the total is = 0 when the Balance is selected as payment method
        iosCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed()
                , "Order success screen isn't displayed");

        // Go to More tab
        iosOrderSuccessScreen.get().pressBackBtn();
        iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressMoreTabBtn();

        iosMoreScreen.get().pressActivityHistoryBtn();
        Assert.assertTrue(iosActivityHistoryScreen.get().isPageDisplayed());

        iosActivityHistoryScreen.get().selectOrdersTab();

        //Assert that Order Card is displayed
        Assert.assertEquals(iosActivityHistoryScreen.get().getDisplayedOrdersCount(), 1,
                "Expected only one order card to be displayed, but found " +
                        iosActivityHistoryScreen.get().getDisplayedOrdersCount());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingOrderWithCouponWithPercentageType() {
        //Create Coupon code via API
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "percentage",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                true,
                "now"));
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get()
                , iosAddressSelectionScreen.get()
                , iosLocationPermissionAlert.get()
                , iosSetAddressScreen.get()
                , iosDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());

        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());

        iosAddressCreateSuccessModal.get().dismissModal();

        defaultTestData.get().getTestOrder().setExpectedDeliveryTime(
                testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy") + " "
                        + (iosCheckoutScreen.get().getInstantDeliveryExpectedTime().contains("before")
                        ? iosCheckoutScreen.get().getInstantDeliveryExpectedTime()
                        : "in " + iosCheckoutScreen.get().getInstantDeliveryExpectedTime()));

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());

        //Apply the coupon created by the API
        iosCheckoutScreen.get().enterCouponCode(defaultTestData.get().getTestCoupon().getCouponCode());
        iosCheckoutScreen.get().pressApplyCouponCodeBtn();

        // Assert the hint msg is shown after applying the code
        Assert.assertTrue(iosCheckoutScreen.get().isCouponCodeErrorMsgDisplayed());

        //Assert the coupon is applied successfully
        Assert.assertEquals(iosCheckoutScreen.get().getCouponCodeMessage(), "Promo code applied successfully.");

        // Update the Payment method to COD
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        iosCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed()
                , "Order success screen is displayed");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingOrderWithCouponWithFixedType (){
        //Create the Coupon code via API
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "fixed",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                false,
                "now"));
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get()
                , iosAddressSelectionScreen.get()
                , iosLocationPermissionAlert.get()
                , iosSetAddressScreen.get()
                , iosDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());

        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());

        iosCreateAddressScreen.get().pressSaveAddressBtn();
        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();
        defaultTestData.get().getTestOrder().setExpectedDeliveryTime(
                testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy") + " "
                        + (iosCheckoutScreen.get().getInstantDeliveryExpectedTime().contains("before")
                        ? iosCheckoutScreen.get().getInstantDeliveryExpectedTime()
                        : "in " + iosCheckoutScreen.get().getInstantDeliveryExpectedTime()));

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());

        //Apply the coupon created by the API
        iosCheckoutScreen.get().enterCouponCode(defaultTestData.get().getTestCoupon().getCouponCode());
        iosCheckoutScreen.get().pressApplyCouponCodeBtn();

        // Assert the hint msg is shown after applying the code
        Assert.assertTrue(iosCheckoutScreen.get().isCouponCodeErrorMsgDisplayed());

        //Assert the coupon is applied successfully
        Assert.assertEquals(iosCheckoutScreen.get().getCouponCodeMessage(), "Promo code applied successfully.");

        // Update the Payment method to COD
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        iosCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed()
                , "Order success screen is displayed");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingOrderWithWrongCouponCode() {
        //Create the coupon code via API
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "percentage",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                true,
                "now"));

        // Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get()
                , iosAddressSelectionScreen.get()
                , iosLocationPermissionAlert.get()
                , iosSetAddressScreen.get()
                , iosDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);

        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());

        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());

        iosAddressCreateSuccessModal.get().dismissModal();

        defaultTestData.get().getTestOrder().setExpectedDeliveryTime(
                testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy") + " "
                        + (iosCheckoutScreen.get().getInstantDeliveryExpectedTime().contains("before")
                        ? iosCheckoutScreen.get().getInstantDeliveryExpectedTime()
                        : "in " + iosCheckoutScreen.get().getInstantDeliveryExpectedTime()));

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());

        //Apply the coupon created by the API
        iosCheckoutScreen.get().enterCouponCode("BFD");
        iosCheckoutScreen.get().pressApplyCouponCodeBtn();

        // Assert the Error msg is shown after applying
        Assert.assertTrue(iosCheckoutScreen.get().isCouponCodeErrorMsgDisplayed());

        // Assert the code is not applied
        Assert.assertNotEquals(iosCheckoutScreen.get().getCouponCodeMessage(), "Promo code applied successfully");

        // Assert the applied coupon is wrong
        Assert.assertTrue(iosCheckoutScreen.get().getCouponCodeMessage().contains("doesn't exist as a coupon."));

        // Update the Payment method to COD
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        iosCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed()
                , "Order success screen is displayed");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingOrderWithExpiredCoupon() {
        //Create a Coupon code via API
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "fixed",
                "commercial",
                90,
                0,
                0,
                "expired",
                true,
                false,
                "now"));

        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        iosCategoryScreen.get().isPageDisplayed();

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);

        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());

        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());

        iosAddressCreateSuccessModal.get().dismissModal();

        defaultTestData.get().getTestOrder().setExpectedDeliveryTime(
                testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy") + " "
                        + (iosCheckoutScreen.get().getInstantDeliveryExpectedTime().contains("before")
                        ? iosCheckoutScreen.get().getInstantDeliveryExpectedTime()
                        : "in " + iosCheckoutScreen.get().getInstantDeliveryExpectedTime()));

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());

        //Apply the coupon created by the API
        iosCheckoutScreen.get().enterCouponCode(defaultTestData.get().getTestCoupon().getCouponCode());
        iosCheckoutScreen.get().pressApplyCouponCodeBtn();

        // Assert the hint msg is shown after applying the code
        Assert.assertTrue(iosCheckoutScreen.get().isCouponCodeErrorMsgDisplayed());

        //Assert the coupon is applied successfully
        Assert.assertNotEquals(iosCheckoutScreen.get().getCouponCodeMessage(), "Promo code applied successfully.");

        // Assert that the coupon code is expired
        Assert.assertTrue(iosCheckoutScreen.get().getCouponCodeMessage().contains("has expired.Sorry!"));

        // Update the Payment method to COD
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        iosCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed()
                , "Order success screen is displayed");
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"),@Tag("web"),@Tag("delivery-capacity-management")})
    public void validatePlacingNowOrderInKsa() {
        //add capacity to an instant slot
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().changeCountryCode();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "1000");
        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 1000);
        Assert.assertTrue(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getInstantFlag());
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
        iosAddressSelectionScreen.get().pressAddressOptionsCloseBtn();
        Assert.assertTrue(iosHomeScreen.get().isNowTxtDisplayed());
//        Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        iosCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed());
        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingTomorrowOrderInKsa() {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

              Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
           iosAddressSelectionScreen.get().pressTomorrowOptionBtn();
         Assert.assertTrue(iosHomeScreen.get().isTomorrowTxtDisplayed());
       //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId());

         //Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);

          iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
             iosCreateAddressScreen.get().pressSaveAddressBtn();

         Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
              iosAddressCreateSuccessModal.get().dismissModal();
        iosCheckoutScreen.get().pressSelectDeliveryTimeInTomorrow();
        Assert.assertNotEquals(iosCheckoutScreen.get().getDisplayedSlotsCount(), 0);
        iosCheckoutScreen.get().pressFirstAvailableTimeSlot();
        iosCheckoutScreen.get().pressPlaceOrderBtn();
       Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateMiniTrackerIsDisplayedAfterPlacingTomorrowOrderInKsa() {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
        iosAddressSelectionScreen.get().pressTomorrowOptionBtn();
        Assert.assertTrue(iosHomeScreen.get().isTomorrowTxtDisplayed());
        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId());

        //Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);

        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();
        iosCheckoutScreen.get().pressSelectDeliveryTimeInTomorrow();
        Assert.assertNotEquals(iosCheckoutScreen.get().getDisplayedSlotsCount(), 0);
        iosCheckoutScreen.get().pressFirstAvailableTimeSlot();
        iosCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed());
        iosOrderSuccessScreen.get().pressBackBtn();
        Assert.assertTrue(iosHomeScreen.get().isMiniTrackerDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"),@Tag("web"),@Tag("delivery-capacity-management")})
    public void validateMiniTrackerDisplayedAfterPlacingNowOrderInKsa() {
        // DCM For KSA not migrated yet but it will be migrated soon, so I added it for the future
//        //add capacity to an instant slot
//        webLoginPage.get().goToLoginPage();
//        webLoginPage.get().changeCountryCode();
//        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
//                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
//        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
//        webDeliveryCapacityManagementPage.get().goToPage();
//        webDeliveryCapacityManagementPage.get().choosefp();
//        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
//        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
//        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "1000");
//        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
//                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 1000);
//        Assert.assertTrue(deliveryCapacityManagementApiClient.get().getAllSlotsData(
//                defaultTestData.get().getAdminUser(),
//                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getInstantFlag());
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
        iosAddressSelectionScreen.get().pressAddressOptionsCloseBtn();
        Assert.assertTrue(iosHomeScreen.get().isNowTxtDisplayed());
//        Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        iosCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed());
        iosOrderSuccessScreen.get().pressBackBtn();
        Assert.assertTrue(iosHomeScreen.get().isMiniTrackerDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingScheduledOrderInKsa() {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
        iosAddressSelectionScreen.get().pressNowModeBtn();
        Assert.assertTrue(iosHomeScreen.get().isNowTxtDisplayed());
        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();
        iosCheckoutScreen.get().pressSelectDeliveryTimeInNow();
        Assert.assertNotEquals(iosCheckoutScreen.get().getDisplayedSlotsCount(), 0);
        iosCheckoutScreen.get().pressFirstAvailableTimeSlotInNow();
        iosCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateMiniTrackerAfterPlacingScheduledOrderInKsa() {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
        iosAddressSelectionScreen.get().pressNowModeBtn();
        Assert.assertTrue(iosHomeScreen.get().isNowTxtDisplayed());
        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();
        iosCheckoutScreen.get().pressSelectDeliveryTimeInNow();
        Assert.assertNotEquals(iosCheckoutScreen.get().getDisplayedSlotsCount(), 0);
        iosCheckoutScreen.get().pressFirstAvailableTimeSlotInNow();
        iosCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(iosOrderSuccessScreen.get().isPageDisplayed());
        iosOrderSuccessScreen.get().pressBackBtn();
        Assert.assertTrue(iosHomeScreen.get().isMiniTrackerDisplayed());
    }
}
