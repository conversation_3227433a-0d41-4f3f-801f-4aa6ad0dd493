package customerApp.androidNative.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.stream.Collectors;

@Test
public class CollectionsTests extends BaseTest {
    @Test(groups = {"collections", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateCreateOrderThroughHomeCollectionIsWorkingCorrectly(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeHomeScreen.get().pressFirstAvailableCollection();
        androidNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        androidNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }

    @Test(groups = {"collections", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateCreateOrderAsGuestThroughHomeCollectionIsWorkingCorrectly() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressExploreBtn();

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeHomeScreen.get().pressFirstAvailableCollection();
        androidNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        androidNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(androidNativePhoneNumberScreen.get().isPageDisplayed());
    }

    @Test(groups = {"collections", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateCreateOrderWithCouponThroughHomeCollectionIsWorkingCorrectly() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeHomeScreen.get().pressFirstAvailableCollection();
        androidNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        androidNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free delivery",
                "commercial",
                0,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCheckoutScreen.get().getPromoCodeTxtFieldContentDescription());

        androidNativeCheckoutScreen.get().enterPromoCode(defaultTestData.get().getTestCoupon().getCouponCode());
        androidNativeCheckoutScreen.get().pressPromoCodeApplyBtn();
        Assert.assertTrue(androidNativeCheckoutScreen.get().isCouponValid());

        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }

    @Test(groups = {"collections", "regression", "deep-regression"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateCreateOrderWithDeliveryNoteThroughHomeCollectionIsWorkingCorrectly() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeHomeScreen.get().pressFirstAvailableCollection();
        androidNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        androidNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());

        androidNativeCheckoutScreen.get().enterADeliveryNote("randomDeliveryNote");
        androidNativeCheckoutScreen.get().hideKeyboardIfDisplayed();
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }

    @Test(groups = {"collections", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateCreateOrderWithTipThroughHomeCollectionIsWorkingCorrectly() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeHomeScreen.get().pressFirstAvailableCollection();
        androidNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        androidNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCheckoutScreen.get().getTipAmountContentDescription());

        androidNativeCheckoutScreen.get().pressTipAmount();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }

    @Test(groups = {"collections", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateCreateOrderWithSufficientBalanceThroughHomeCollectionIsWorkingCorrectly() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1000.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeHomeScreen.get().pressFirstAvailableCollection();
        androidNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        androidNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }

    @Test(groups = {"collections", "regression", "deep-regression"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateCreateOrderWithInSufficientBalanceThroughHomeCollectionIsWorkingCorrectly() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeHomeScreen.get().pressFirstAvailableCollection();
        androidNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        androidNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }

    @Test(groups = {"collections", "regression", "deep-regression"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateCreateOrderWithDueBalanceThroughHomeCollectionIsWorkingCorrectly() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-100.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeHomeScreen.get().pressFirstAvailableCollection();
        androidNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        androidNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }
}
