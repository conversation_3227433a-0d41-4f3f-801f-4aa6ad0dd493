package customerApp.androidNative.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.util.stream.Collectors;
public class PlaceOrderTests extends BaseTest {
    @Test(groups = {"place-order", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void PlacingInstantOrder(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(
                        defaultTestData.get()
                                .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // click on place order
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");

        androidNativeOrderDetailsScreen.get().pressBackBtn();
        Assert.assertTrue(androidNativeHomeScreen.get().placeOrderMiniTrackingIsDisplayed());
    }

    @Test(groups = {"place-order", "regression", "deep-regression"})
    @Tags({@Tag("android")
            , @Tag("customer-app-native")
            , @Tag("mobile-shopping")})
    public void PlaceOrderInBusyFP(){
        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                0);

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(
                        defaultTestData.get()
                                .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());

        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());

        //busy message appear
        Assert.assertTrue(androidNativeCheckoutScreen.get().isBusyMessageDisplayed());
    }

    @Test(groups = {"place-order", "regression", "deep-regression"})
    @Tags({@Tag("android")
            , @Tag("customer-app-native")
            , @Tag("web")
            , @Tag("delivery-capacity-management")
            , @Tag("mobile-shopping")})
    public void FPBusyWhileClickingPlaceOrderAndAppearModalWithVeryNextAvailableTimeslot(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(
                        defaultTestData.get()
                                .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());

        // Make sure capacity is 0 to FP
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");

        // click on place order
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        //busy message appear
        Assert.assertTrue(androidNativeCheckoutScreen.get().isBusyMessagePopupDisplayed());
    }

    // NOTE: This test can't be executed past 9 PM(Cairo Time). It will fail due to the future timeslots being unavailable
    @Test(groups = {"place-order", "regression", "deep-regression"})
    @Tags({@Tag("android")
            , @Tag("customer-app-native")
            , @Tag("web")
            , @Tag("delivery-capacity-management")
            , @Tag("mobile-shopping")})
    public void forcedSchedulingConfirmationMessageWhenNextTimeslotGreaterThan3HFromNow(){
        testExecutionHelper.get().skipTestsBasedOnCutOffTime("21");

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(
                        defaultTestData.get()
                                .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(6), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(7), "100");

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        Assert.assertTrue(androidNativeCheckoutScreen.get().closestSlotDueToHighDemandMsgIsDisplayed());

        Assert.assertTrue(androidNativeCheckoutScreen.get().bookThisSlotBtnIsDisplayed());

        androidNativeCheckoutScreen.get().pressBookThisSlotBtn();

        Assert.assertTrue(androidNativeCheckoutScreen.get().scheduleBtnIsDisplayed());
        Assert.assertTrue(androidNativeCheckoutScreen.get().deliverAtTheEarliestToggleIsDisplayed());

        androidNativeCheckoutScreen.get().pressDeliverAtTheEarliestToggleBtn();
        Assert.assertTrue(androidNativeCheckoutScreen.get().deliverAtTheEarliestPopupSheetIsDisplayed());
        androidNativeCheckoutScreen.get().pressGotItBtn();
        androidNativeCheckoutScreen.get().pressDeliverAtTheEarliestToggleBtn();

        androidNativeCheckoutScreen.get().pressDeliverAtTheEarliestToggleBtn();
        Assert.assertTrue(androidNativeCheckoutScreen.get().deliverAtTheEarliestPopupSheetIsDisplayed());
        androidNativeCheckoutScreen.get().pressGotItBtn();
        androidNativeCheckoutScreen.get().pressDeliverAtTheEarliestToggleBtn();

        androidNativeCheckoutScreen.get().pressDeliverAtTheEarliestToggleBtn();
        Assert.assertTrue(androidNativeCheckoutScreen.get().deliverAtTheEarliestPopupSheetIsDisplayed());
        androidNativeCheckoutScreen.get().pressGotItBtn();
        androidNativeCheckoutScreen.get().pressDeliverAtTheEarliestToggleBtn();

        androidNativeCheckoutScreen.get().pressDeliverAtTheEarliestToggleBtn();
        Assert.assertTrue(androidNativeCheckoutScreen.get().isDeliverAtTheEarliestPopupSheetHidden());

        // click on place order
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());
    }

    // NOTE: This test can't be executed past 9 PM(Cairo Time). It will fail due to the future timeslots being unavailable
    @Test(groups = {"place-order", "regression", "deep-regression"})
    @Tags({@Tag("android")
            , @Tag("customer-app-native")
            , @Tag("web")
            , @Tag("delivery-capacity-management")
            , @Tag("mobile-shopping")})
    public void forcedSchedulingConfirmationMessageWhenNextTimeslotGreaterThan3HFromNowMoreThanOneAvailableSlots() {
        testExecutionHelper.get().skipTestsBasedOnCutOffTime("21");

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(
                        defaultTestData.get()
                                .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        // instant slot is busy and set Capacity after 6h,7h and 8h
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(6), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(7), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(8), "100");

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        Assert.assertTrue(androidNativeCheckoutScreen.get().closestSlotDueToHighDemandMsgIsDisplayed());

        Assert.assertTrue(androidNativeCheckoutScreen.get().bookThisSlotBtnIsDisplayed());
        Assert.assertTrue(androidNativeCheckoutScreen.get().pickAnotherTimeBtnIsDisplayed());

        androidNativeCheckoutScreen.get().pressPickAnotherTimeBtn();

        Assert.assertTrue(androidNativeCheckoutScreen.get().scheduleAnotherTimeSheetIsDisplayed());

        androidNativeCheckoutScreen.get().pressFirstAvailableScheduleSlot();

        // click on place order
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());
    }

    // NOTE: This test can't be executed past 9 PM(Cairo Time). It will fail due to the future timeslots being unavailable
    @Test(groups = {"place-order", "regression", "deep-regression"})
    @Tags({@Tag("android")
            , @Tag("customer-app-native")
            , @Tag("web")
            , @Tag("delivery-capacity-management")
            , @Tag("mobile-shopping")})
    public void CapacityChangedBeforePlaceOrderToBeAvailableAfter3h(){
        testExecutionHelper.get().skipTestsBasedOnCutOffTime("21");

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(
                        defaultTestData.get()
                                .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // instant slot is busy and set Capacity available after 6h
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(6), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(7), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(8), "100");

        // click on place order
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        //Delivery Time Updated popup
        Assert.assertTrue(androidNativeCheckoutScreen.get().isDeliveryTimeUpdatedPopupDisplayed());
        androidNativeCheckoutScreen.get().pressPlaceOrderDeliveryTimeUpdatedPopup();

        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());
    }

    // NOTE: This test can't be executed past 9 PM(Cairo Time). It will fail due to the future timeslots being unavailable
    @Test(groups = {"place-order", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("android")
            , @Tag("customer-app-native")
            , @Tag("web")
            , @Tag("delivery-capacity-management")
            , @Tag("mobile-shopping")})
    public void PlacingAndTrackingScheduledOrder() {
        testExecutionHelper.get().skipTestsBasedOnCutOffTime("21");

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(
                        defaultTestData.get()
                                .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(2), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(3), "100");
        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(4), "100");

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        Assert.assertTrue(androidNativeCheckoutScreen.get().scheduleBtnIsDisplayed());
        androidNativeCheckoutScreen.get().pressScheduleBtn();

        Assert.assertTrue(androidNativeCheckoutScreen.get().scheduleAnotherTimeSheetIsDisplayed());

        androidNativeCheckoutScreen.get().pressFirstAvailableInScheduleAnotherTimeSheet();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        androidNativeOrderDetailsScreen.get().pressBackBtn();
        Assert.assertTrue(androidNativeHomeScreen.get().placeOrderMiniTrackingIsDisplayed());
    }
}
