package customerApp.androidNative.foodAggregators;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class PlaceOrderTests extends BaseTest {
    @Test(groups = {"aggregators-regression", "B10-11223"})
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping"), @Tag("food-aggregator")})
    public void validateOrderIsPlacedSuccessfully() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        androidNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed();

        androidNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , "down"
                , "id=" + androidNativeFoodAggregatorHomeScreen.get().getRestaurantCardIdSelector(
                        String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsList().getFirst().getYeloId())));

        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantCardById(String.valueOf(
                defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().getFirst().getYeloId()));

        androidNativeFoodAggregatorResturantDetailsScreen.get().isPageDisplayed();

        androidNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(androidNativeFoodAggregatorResturantDetailsScreen.get().isFirstProductAddedToCart());

        androidNativeFoodAggregatorResturantDetailsScreen.get().goToCartScreen();
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressAddNewCardPaymentOption();
        Assert.assertTrue(androidNativeCheckoutScreen.get().isCardSelectionModalDisplayed());
        androidNativeCheckoutScreen.get().pressChangeCardBtn();
        Assert.assertFalse(androidNativeCheckoutScreen.get().isCardSelectionModalDisplayed());
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(androidAddCardInfoScreen.get().isPageDisplayed());
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard(),
                defaultTestData.get().getTestExpiryDate(),
                defaultTestData.get().getTestCVC(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                defaultTestData.get().getRandomTestUser().getFullName());
        androidAddCardInfoScreen.get().pressAddCardBtn();
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());

        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());
    }
}
