package customerApp.androidNative.authentication;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class LoginTests extends BaseTest {
    @Test(groups = {"B10-12345", "smoke"})
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void loginWithValidLocalPhoneNumber(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
    }

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void enterWrongOtpCodeDuringLogin(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativePhoneNumberScreen.get().enterPhoneNumberAndPresNext(
                defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        Assert.assertTrue(androidNativeOtpVerificationScreen.get().isPageDisplayed());

        androidNativeOtpVerificationScreen.get().isWrongOtpErrorMsgDisplayed();

        androidNativeOtpVerificationScreen.get().enterOtp("0002");
        Assert.assertTrue(androidNativeOtpVerificationScreen.get().getWrongOtpErrorMsgText().contains("2"));
        androidNativeOtpVerificationScreen.get().isErrorMsgDismissed();

        // submit OTP wrong for 3 times to validate the error message
        androidNativeOtpVerificationScreen.get().enterOtp("0001");
        Assert.assertTrue(androidNativeOtpVerificationScreen.get().getWrongOtpErrorMsgText().contains("1"));
        androidNativeOtpVerificationScreen.get().isErrorMsgDismissed();

        androidNativeOtpVerificationScreen.get().enterOtp("0000");
        Assert.assertTrue(androidNativeOtpVerificationScreen.get().getWrongOtpErrorMsgText().contains("0"));
        androidNativeOtpVerificationScreen.get().isErrorMsgDismissed();

        androidNativeOtpVerificationScreen.get().enterOtp("0003");
        Assert.assertTrue(androidNativeOtpVerificationScreen.get().isOutOfVerificationAttemptsErrorMsgDisplayed());
    }

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void validateRegexOfPhoneNumberField(){
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativePhoneNumberScreen.get().enterPhoneNumber(" ");
        androidNativePhoneNumberScreen.get().pressNextBtn();
        Assert.assertTrue(androidNativePhoneNumberScreen.get().isRequiredMobileNumberErrorMsgDisplayed());

        androidNativePhoneNumberScreen.get().enterPhoneNumber("111abcdefg");
        androidNativePhoneNumberScreen.get().pressNextBtn();
        Assert.assertTrue(androidNativePhoneNumberScreen.get().isInvalidFormatErrorMsgDisplayed());

        androidNativePhoneNumberScreen.get().clearPhoneNumberTxtField();
        androidNativePhoneNumberScreen.get().enterPhoneNumber("1114567891");
        Assert.assertTrue(androidNativePhoneNumberScreen.get().isNextBtnEnabled());
    }
}
