package customerApp.androidNative.authentication;

import base.BaseTest;
import helpers.factories.dataFactories.OtpFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class RegisterTests extends BaseTest {
    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void registerAndLoginWithLocalValidPhoneNumber(){
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativePhoneNumberScreen.get().enterPhoneNumberAndPresNext(
                defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "register", defaultTestData.get().getRandomTestUser()));

        Assert.assertTrue(androidNativeOtpVerificationScreen.get().getDidNotGetCodeBtnText()
                .contains("Resend code"));

        androidNativeOtpVerificationScreen.get().enterOtp(defaultTestData.get().getRandomTestUser().getOtp());

        androidNativeCreateAccountScreen.get().fillInAccountInformationForm(defaultTestData.get());
        androidNativeCreateAccountScreen.get().pressSubmitBtn();

        Assert.assertTrue(androidNativeRegisterSuccessScreen.get().isPageDisplayed());
        androidNativeRegisterSuccessScreen.get().pressDoneBtn();

        androidNativeHomeScreen.get().pressMoreBtn();
        Assert.assertTrue(androidNativeMoreScreen.get().getDisplayedUserFullName()
                .equalsIgnoreCase(defaultTestData.get().getRandomTestUser().getFullName()));

        androidNativeHomeScreen.get().pressHomeBtn();

        Assert.assertNotNull(switcherApiClient.get().searchForUser(defaultTestData.get().getRandomTestUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser()).getId());

        androidNativeTestsExecutionHelper.get().logoutAndGoToPhoneInputScreen(androidDriver.get(),
                androidNativeHomeScreen.get(),
                androidNativeMoreScreen.get());

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
    }

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void validateRegexOfRegisterForm(){
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativePhoneNumberScreen.get().enterPhoneNumberAndPresNext(
                defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "register", defaultTestData.get().getRandomTestUser()));

        androidNativeOtpVerificationScreen.get().enterOtp(defaultTestData.get().getRandomTestUser().getOtp());

        Assert.assertTrue(androidNativeCreateAccountScreen.get().isPageDisplayed());

        androidNativeCreateAccountScreen.get().outFocusCursorFromTxtField();
        Assert.assertTrue(androidNativeCreateAccountScreen.get().isEmptyTxtFieldErrorMsgDisplayed());
        androidNativeCreateAccountScreen.get().enterFirstName("1123abcde@@");
        androidNativeCreateAccountScreen.get().outFocusCursorFromTxtField();
        Assert.assertTrue(androidNativeCreateAccountScreen.get().isInvalidFirstNameErrorMsgDisplayed());
        androidNativeCreateAccountScreen.get().clearTxtField("firstname");
        androidNativeCreateAccountScreen.get().enterFirstName(defaultTestData.get().getRandomTestUser().getFirstName());

        androidNativeCreateAccountScreen.get().pressTxtField("lastname");
        androidNativeCreateAccountScreen.get().enterLastName("");
        androidNativeCreateAccountScreen.get().outFocusCursorFromTxtField();
        Assert.assertTrue(androidNativeCreateAccountScreen.get().isEmptyTxtFieldErrorMsgDisplayed());
        androidNativeCreateAccountScreen.get().enterLastName("1123abcde@@");
        androidNativeCreateAccountScreen.get().outFocusCursorFromTxtField();
        Assert.assertTrue(androidNativeCreateAccountScreen.get().isInvalidLastNameErrorMsgDisplayed());
        androidNativeCreateAccountScreen.get().clearTxtField("lastname");
        androidNativeCreateAccountScreen.get().enterLastName(defaultTestData.get().getRandomTestUser().getLastName());

        Assert.assertTrue(androidNativeCreateAccountScreen.get().isSubmitBtnEnabled());

        androidNativeCreateAccountScreen.get().pressTxtField("email");
        androidNativeCreateAccountScreen.get().enterEmail("1212323abcdef@");
        androidNativeCreateAccountScreen.get().outFocusCursorFromTxtField();
        Assert.assertTrue(androidNativeCreateAccountScreen.get().isInvalidEmailErrorMsgDisplayed());
        androidNativeCreateAccountScreen.get().clearTxtField("email");
        androidNativeCreateAccountScreen.get().enterEmail(defaultTestData.get().getRandomTestUser().getEmailAddress());
        androidNativeCreateAccountScreen.get().pressSubmitBtn();

        Assert.assertTrue(androidNativeRegisterSuccessScreen.get().isPageDisplayed());
    }
}
