package customerApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.apache.commons.math3.util.Precision;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.time.Duration;
import java.util.Collections;
import java.util.stream.Collectors;

public class CreditCardOrderTests extends BaseTest {

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCWithFreeDeliveryCouponAndTippingDuringCheckout() throws InterruptedException {

        //Create Free delivery coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free delivery",
                "commercial",
                0,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert delivery fees = 0
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getTestOrder().getDeliveryFees(), 0);

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndPartialBalanceWithBackToWalletCoupon() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Create Back to wallet coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "commercial",
                100,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        //TODO:ISSUE IN TESTING TO BE REPORTED
//        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
//                ,5,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-5));
        //Assert that the wallet payment is equal to the value existing
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                , "5.00");
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert wallet transaction ID is correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");
        //Assert wallet transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"SUCCESS");

        //Assert wallet Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId()),
                "SUCCESS");

        //Wait for coupon value to reflect in balance
        Thread.sleep(Duration.ofSeconds(60));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 100 (coupon value)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                100.0f);

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,"5.00");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndDueAmountTippingFromCCAfterCompletion() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to -5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,true);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),5);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a CC transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-5));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 0 (Due amount paid)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0.0f);

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Assert gratuity CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcGratuityTransactionId()),
                "SUCCESS");

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndDueAmountWithBackToWalletFixedCoupon() throws InterruptedException {

        //Create Back to wallet coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "commercial",
                30,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to -30
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-30.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Wait for coupon value to reflect in balance
        Thread.sleep(Duration.ofSeconds(60));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert User Balance has due amount paid & coupon Reflected
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 30.0);
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCWithTippingDuringCheckoutThenMarkOrderAsNotReceived() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert payment transaction status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "AUTHORIZED");
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Await order to turn to processing
        Thread.sleep(Duration.ofMinutes(1));

        //Mark order as not-received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status in get user order details
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received");

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getStatus(),"not-received");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "VOIDED");

    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndDueAmountWithBackToWalletPercentageCoupon() throws InterruptedException {

        //Create Back to wallet coupon with percentage value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                true,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to -30
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-30.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 5.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Wait for coupon value to reflect in balance
        Thread.sleep(Duration.ofSeconds(60));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert User Balance has due amount paid , coupon & collected amount Reflected
        // Cart Value = total-20(delivery)-30(due amount)
        //Coupon value = cart value rounded * percentage of discount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                Precision.round(((defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 30.0f
                        - defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()) * 0.1f) + 5, 2));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndTippingDuringCheckoutAndPartialRefundOrderThroughSwitcherWithGratuityIssueReasonToCreditCard() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // refund order with gratuity issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                Float.valueOf(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()),
                false,
                "gratuity issue",
                0);

        Thread.sleep(Duration.ofSeconds(70));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Credit Card");

        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(
                String.format("%.0f", Double.parseDouble(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount())),
                defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()
        );

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndPartialWalletAndTippingDuringCheckoutAndPartialRefundOrderThroughSwitcherWithGratuityIssueReasonToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // refund order with gratuity issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                Float.valueOf(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()),
                true,
                "gratuity issue",
                0);

        Thread.sleep(Duration.ofSeconds(30));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(
                String.format("%.0f", Double.parseDouble(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount())),
                defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()
        );

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndTippingDuringCheckoutAndPartialRefundOrderThroughSwitcherWithGratuityIssueReasonToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // refund order with gratuity issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees(),
                true,
                "gratuity-issue",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(
                String.format("%.0f", Double.parseDouble(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount())),
                defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()
        );

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be");

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getCurrentBalance(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount());

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCWithBackToWalletPercentageCouponAndCollectedAmountGreaterThanOrderAmount() throws InterruptedException {

        //Create Back to wallet coupon with percentage value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                true,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(),
                "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),
                20, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Wait for 1 min to ensure the coupon value reflected in balance
        Thread.sleep(Duration.ofMinutes(1));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be");

        //Assert User Balance has order amount deducted , coupon & collected amount Reflected
        // Cart Value = total-20(delivery)
        // Coupon value = cart value rounded * percentage of discount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                (Precision.round((20.0f
                        + ((defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                        - defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()) * 0.1f)), 2)));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndFreeGiftCoupon() throws InterruptedException {

        //Create free gift coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createDiscountProductOrFreeGiftCoupon(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free gift",
                "commercial",
                1,
                "active",
                true,
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct(),
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false,
                "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert number of products in order contains free gift item
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts().size(), 3);

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createScheduleOrderWithCCAndTippingFromCCAfterCompletion() throws InterruptedException {

        // Update Fbs capacity to current timeslot
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                90);

        // Update Fb capacity to current timeslot = 0
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                Collections.singletonList(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId()).getFirst().getCurrentTimeslotId()),
                0);

        //Create Back to wallet coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 30
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("30.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(),
                false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(),
                "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),
                0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that Order is schedule
        Assert.assertTrue(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().isScheduled());

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // wait until balance reflected
        Thread.sleep(Duration.ofSeconds(120));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 40
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                40.0f);

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(),
                false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Assert gratuity CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcGratuityTransactionId()),
                "SUCCESS");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCWithDueAmountAndMarkOrderAsCancelled() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to -5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-5"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,true);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),5);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "AUTHORIZED");
        //Assert There is a CC transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-5));

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Mark order as cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",true);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .getFirst()
                        .getStatus(),
                "cancelled",
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
        );

        //Wait for 120 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofSeconds(120));

        //Update random test user balance with current balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert balance still has the same amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                -5
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "VOIDED");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndDueAmountAndNotReceived() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to -30
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-30.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Mark order as not-received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Void Payment
        paymentPanelApiClient.get().voidPayment(defaultTestData.get().getPaymentPanelUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Assert order is marked as not received
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Wait for 120 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofSeconds(120));

        //Update random test user balance with current balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert balance still has the same amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                -30
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndTippingDuringRatingAndPartialRefundOrderThroughPaymentPanelToCreditCard() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        Thread.sleep(10000);

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeGratuityOrderTransactionType(), "PAYMENT");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityTransactionId(),
                false,
                5
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundGratuityOrderTransactionType(), "PAYMENT");

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndPartialBalanceAndPartialRefundOrderThroughPaymentPanelToCreditCard() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                false,
                20
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionType(), "CHARGE");

        //Assert Order Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundCCPaymentTransactionAmount(), 20);

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndFullyRefundOrderThroughPaymentPanelToCreditCard() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        Thread.sleep(60000);

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                false,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundCCPaymentTransactionType(), "REFUND");

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndCollectedAmountGreaterThanOrderAmountAndTippingDuringRatingAndFullyRefundOrderThroughPaymentPanelToCreditCard() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() + 20, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeGratuityOrderTransactionType(), "PAYMENT");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                false,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundCCPaymentTransactionType(), "REFUND");

    }
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndTippingDuringRatingAndCollectedAmountGreaterThanOrderAmountAndFullyRefundOrderThroughPaymentPanelToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() + 20, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeGratuityOrderTransactionType(), "PAYMENT");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        //Assert balance != 0
        Assert.assertNotEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0);

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndPartialBalanceAndFullyRefundOrderThroughPaymentPanelToCreditCard() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeGratuityOrderTransactionType(), "PAYMENT");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                false,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundCCPaymentTransactionType(), "REFUND");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndPartialBalanceAndTippingDuringRatingAndFullyRefundOrderThroughPaymentPanelToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

//        //Assert Payment successful
//        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeGratuityOrderTransactionType(), "PAYMENT");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        //Assert balance = order total + wallet amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() + 5.0);

        //Assert balance != 0
        Assert.assertNotEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0);

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndTippingDuringRatingAndPartialRefundOrderThroughPaymentPanelToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeGratuityOrderTransactionType(), "PAYMENT");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityTransactionId(),
                true,
                1
        );

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        //Assert balance = 1
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                1);

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundGratuityOrderTransactionType(), "WALLET");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndPartialBalanceAndPartialRefundOrderThroughPaymentPanelToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        Thread.sleep(Duration.ofSeconds(15));

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                20
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        //Assert balance = 25
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                25);

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndTippingDuringRatingAndFullyRefundOrderThroughPaymentPanelToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeGratuityOrderTransactionType(), "PAYMENT");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        //Assert balance = order total
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndTippingDuringCheckoutAndFullyRefundOrderWithProductIssueReasonToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // refund order with product issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                true,
                "order bad experience",
                0);

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        //Assert balance = order total
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());

    }
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndTippingDuringCheckoutAndRefundOrderWithLateDeliveryIssueToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // refund order with delivery issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees(),
                true,
                "delivery issue",
                0);

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        //Assert balance = 13
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
               13);

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndPartialBalanceAndRefundOrderWithLateDeliveryIssueToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // refund order with delivery issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees(),
                true,
                "delivery issue",
                0);

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        //Assert balance = 18.0
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                18.0);

    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndTippingDuringCheckoutAndPartialRefundOrderWithGratuityIssueReasonToCreditCard() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // refund order with gratuity issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees(),
                false,
                "gratuity-issue",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Credit Card");

        Thread.sleep(Duration.ofSeconds(30));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount = delivery fees
        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees(),
                0.01
        );

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndFullyRefundOrderThroughSwitcherToCreditCard() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // refund order with product issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                false,
                "order bad experience",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Credit Card");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                0.01 // Tolerance for floating-point precision issues
        );

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndPartialBalanceAndTippingDuringRatingAndFullyRefundOrderThroughSwitcherToCreditCard() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeGratuityOrderTransactionType(), "PAYMENT");

        // refund order with product issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                false,
                "order bad experience",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Credit Card");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                0.01 // Tolerance for floating-point precision issues
        );
    }
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndFullyRefundOrderThroughSwitcherWithProductIssueReasonToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // refund order with product issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                true,
                "order bad experience",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                0.01
        );

    }
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndPartialBalanceAndTippingDuringRatingAndFullyRefundOrderThroughSwitcherToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeGratuityOrderTransactionType(), "PAYMENT");

        // refund order with order bad experience reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                true,
                "order bad experience",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount = order total amount
        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                0.01
        );
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderPartialBalanceAndPartialRefundOrderThroughSwitcherWithOrderBadExperienceToCreditCard() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        // refund order with order bad experience reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                2,
                false,
                "order bad experience",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Credit Card");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                2, 0.01);
    }
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderAndPartialRefundOrderThroughSwitcherWithDeliveryIssueReasonToCreditCard() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        // refund order with delivery issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(),
                false,
                "delivery issue",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundType());

        Thread.sleep(Duration.ofSeconds(60));

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Credit Card");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                        defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(), 0.01);
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndPartialBalanceAndFullyRefundOrderThroughSwitcherWithBadOrderExperienceIssueToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"gratuity");

        //Assert Gratuity Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeGratuityOrderTransactionType(), "PAYMENT");

        // refund order with order bad experience reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                true,
                "order bad experience",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount = order total amount
        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                0.01
        );
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndPartialBalanceAndTippingDuringCheckoutAndPartialRefundOrderThroughSwitcherWithDeliveryIssueReasonToCreditCard() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // refund order with delivery issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                        defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(),
                false,
                "delivery issue",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundType());

        Thread.sleep(Duration.ofSeconds(60));

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Credit Card");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                        defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(), 0.01);
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void createOrderWithCCAndTippingDuringCheckoutAndPartialRefundOrderThroughSwitcherWithDeliveryIssueReasonToWallet() throws InterruptedException {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        // refund order with delivery issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                        defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(),
                true,
                "delivery issue",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundType());

        Thread.sleep(Duration.ofSeconds(60));

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount = order total amount
        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                        defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(),
                0.01
        );

        //Assert Refund amount = balance
        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0.01
        );

    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndPartialBalance() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-5));
        //Assert that the wallet payment is equal to the value existing
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                , "5.00");
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert wallet transaction ID is correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");
        //Assert wallet transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"SUCCESS");

        //Assert wallet Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId()),
                "SUCCESS");

        //Wait for coupon value to reflect in balance
        Thread.sleep(Duration.ofSeconds(60));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 0 (balance has been used in order)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0);

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,"5.00");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndDueAmountTippingFromCheckout() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to -30
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-30.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,true);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),30);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a CC transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-30));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 0 (Due amount paid)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0.0f);

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCC() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"SUCCESS");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 0 as it started
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0);

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,"0.00");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndPercentageCartCoupon() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Create Percentage Cart coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "percent",
                "commercial",
                0,
                0,
                10,
                "active",
                true,
                true,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,true);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is =  order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"SUCCESS");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 0 as it started
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0);

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,"0.00");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCTippingFromCheckoutAndFixedCartCoupon() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Create Fixed Cart coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "fixed_cart",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
//        createOrderApiValidator.get().assertProductListInResponse(
//                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
//                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Assert Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "10","0");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is = order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"SUCCESS");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 0 as it started
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0);

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,"0.00");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndDueAmountAndPercentageCartCoupon() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Create Percentage Cart coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "percent",
                "commercial",
                0,
                0,
                10,
                "active",
                true,
                true,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to -5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),true,true);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),5);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a CC transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-5));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId()),
                "SUCCESS");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 0 (Due amount paid)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0.0f);

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "cc");

        //Create & Pay for gratuity in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "gratuity");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "gratuity",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Assert gratuity CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcGratuityTransactionId()),
                "SUCCESS");

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCDueAmountTippingFromCheckoutAndFixedCartCoupon() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Create Fixed Cart coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "fixed_cart",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to -5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),5);
        //Assert Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "10","0");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is = order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-5));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"SUCCESS");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 0 as it started
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0);

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,"0.00");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndPartialBalanceTippingFromCheckoutWithBackToWalletCoupon() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Create Back to wallet coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "commercial",
                100,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,true);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()-5));
        //Assert that the wallet payment is equal to the value existing
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                , "5.00");
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert wallet transaction ID is correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");
        //Assert wallet transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"SUCCESS");

        //Assert wallet Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId()),
                "SUCCESS");

        //Wait for coupon value to reflect in balance
        Thread.sleep(Duration.ofSeconds(60));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 100 (coupon value)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                100.0f);

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,"5.00");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithInvalidCC() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",false));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment failed | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert payment transaction status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "FAILED");
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertTrue(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus().contains("FAILED"));

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order does not show in user orders
        Assert.assertTrue(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()).isEmpty());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createCCAndPartialWalletOrderWithInvalidCC() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",false));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment failed | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert payment transaction status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "FAILED");
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-5));
        //Assert that the wallet payment is equal to the value existing
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                , "5.00");
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert wallet transaction ID is correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertTrue(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus().contains("FAILED"));
        //Assert wallet transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order does not show in user orders
        Assert.assertTrue(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()).isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert balance returned to wallet
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 5
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndPartialBalanceAndCancel() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert payment transaction status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "AUTHORIZED");
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-5));
        //Assert that the wallet payment is equal to the value existing
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                , "5.00");
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert wallet transaction ID is correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");
        //Assert wallet transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Mark order as cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",true);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .getFirst()
                        .getStatus(),
                "cancelled",
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
        );

        //Wait for 120 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofSeconds(120));

        //Update random test user balance with current balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert balance still has the same amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                5
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "VOIDED");
        //Assert wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndPartialWalletTippingFromCheckoutThenMarkOrderAsNotReceived() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert payment transaction status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "AUTHORIZED");
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-5));
        //Assert There is a wallet transaction and its amount is = wallet value
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,"5.00");
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert wallet Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");
        //Assert wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Await order to turn to processing
        Thread.sleep(Duration.ofMinutes(1));

        //Mark order as not-received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status in get user order details
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received");

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getStatus(),"not-received");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Await transaction becomes voided
        Thread.sleep(Duration.ofMinutes(1));

        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "VOIDED");
        //Assert wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCThenVoidPayment() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Void Payment From Payment Panel
        paymentPanelApiClient.get().voidPayment(defaultTestData.get().getPaymentPanelUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());

        //Await payment tp get voided
        Thread.sleep(Duration.ofMinutes(1));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert order transaction status is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "VOIDED");
        //Assert CC transaction in payment Panel is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "VOIDED");
        //Assert CC Transaction Status is Voided in inai
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"VOIDED");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is Processing
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 0 as it started
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0);
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndPartialBalanceThenVoidPayment() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-5));
        //Assert that the wallet payment is equal to the value existing
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                , "5.00");
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert wallet transaction ID is correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");
        //Assert wallet transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Void Payment From Payment Panel
        paymentPanelApiClient.get().voidPayment(defaultTestData.get().getPaymentPanelUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());

        //Await payment to get voided
        Thread.sleep(Duration.ofMinutes(1));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert order transaction status is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "VOIDED");
        //Assert CC transaction in payment Panel is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "VOIDED");
        //Assert Wallet transaction in payment Panel is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");
        //Assert CC Transaction Status is Voided in inai
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"VOIDED");
        //Assert wallet Transaction Status is voided
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId()),
                "VOIDED");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is Processing
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 5 as it started
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                5);
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCWithTippingAndMarkOrderAsCancelled() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "5" , "0");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "AUTHORIZED");
        //Assert There is a CC transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Mark order as cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",true);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .getFirst()
                        .getStatus(),
                "cancelled",
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
        );

        //Wait for 120 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofSeconds(120));

        //Update random test user balance with current balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert balance still has the same amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "VOIDED");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithTippingAndInvalidCC() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",false));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment failed | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "5" , "0");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert payment transaction status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "FAILED");
        //Assert There is a CC transaction and its amount is correct
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertTrue(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus().contains("FAILED"));

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order does not show in user orders
        Assert.assertTrue(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()).isEmpty());
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndTippingThenVoidPayment() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "5" , "0");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is = order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Void Payment From Payment Panel
        paymentPanelApiClient.get().voidPayment(defaultTestData.get().getPaymentPanelUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());

        //Await payment tp get voided
        Thread.sleep(Duration.ofMinutes(1));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert order transaction status is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "VOIDED");
        //Assert CC transaction in payment Panel is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "VOIDED");
        //Assert CC Transaction Status is Voided in inai
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"VOIDED");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is Processing
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 0 as it started
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0);
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndPartialBalanceWithTippingDuringCheckoutOut() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "5" , "0");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()-5));
        //Assert that the wallet payment is equal to the value existing
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                , "5.00");
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert wallet transaction ID is correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");
        //Assert wallet transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"SUCCESS");

        //Assert wallet Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId()),
                "SUCCESS");

        //Wait for coupon value to reflect in balance
        Thread.sleep(Duration.ofSeconds(60));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 0
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0);

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertTrue(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Credit/Debit Card");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,"5.00");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web"), @Tag("database")})
    public void createOrderWithCCAndPartialBalanceWithTippingThenVoidPayment() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false, false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "5" , "0");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert total Amount of order transaction
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert payment transaction status
        //Assert There is a CC transaction and its amount is = wallet value - order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()-5));
        //Assert that the wallet payment is equal to the value existing
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                , "5.00");
        //Assert CC Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeCCPaymentTransactionId());
        //Assert wallet transaction ID is correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert cc Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "AUTHORIZED");
        //Assert wallet transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Void Payment From Payment Panel
        paymentPanelApiClient.get().voidPayment(defaultTestData.get().getPaymentPanelUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());

        //Await payment tp get voided
        Thread.sleep(Duration.ofMinutes(1));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert order transaction status is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "VOIDED");
        //Assert CC transaction in payment Panel is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeCCPaymentTransactionStatus(), "VOIDED");
        //Assert CC Transaction Status is Voided in inai
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"VOIDED");
        //Assert Wallet transaction in payment Panel is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is Processing
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 5 as it started
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                5);
    }
}
