package models;

import java.util.ArrayList;
import java.util.List;

public class Configs {
    private String chromeDriverPath;
    private String browser;
    private String gridURL;
    private String baseURL;
    private String controlRoomBaseURL;
    private String midMileAppBaseURL;
    private String logisticsBaseURL;
    private String fleetAppBaseURL;
    private String fleetServiceBaseURL;
    private String billingServicesBaseURL;
    private String cardServicesBaseURL;
    private String cardServicesAdminPanelBaseURL;
    private String pickerServicesBaseURL;
    private String paymentServiceBaseURL;
    private String inaiBaseURL;
    private String internalOrdersBaseURL;
    private String browserStackBaseURL;
    private String catalogBaseURL;
    private String testEmail;
    private String testPassword;
    private String testCountryCode;
    private String testMobileNumber;
    private String testMobileCompany;
    private String testCreditCard;
    private String testExpDate;
    private String testCVC;
    private String secondaryTestCreditCard;
    private String secondaryTestExpDate;
    private String secondaryTestCVC;
    private String declinedCreditCard;
    private String webRunMode;
    private String mobileRunMode;
    private String internalAPIToken;
    private String ngrokAuthToken;
    private String slackApiBaseURL;
    private String slackApiToken;
    private Boolean webBuildEnabled;
    private Boolean mobileBuildEnabled;
    private String remoteProviderName;
    private String bStackUserName;
    private String bStackAccessKey;
    private String bStackAppiumVersionForAndroidTests;
    private String bStackAppiumVersionForIosTests;
    private String bStackLocal;
    private String bStackDebug;
    private String bStackNetworkLogs;
    private String bStackSelfHeal;
    private String bStackAndroidCustomerAppNativeAppId;
    private String bStackAndroidCustomerAppNativeAppBuildNumber;
    private String bStackAndroidCustomerAppReactNativeId;
    private String bStackAndroidCustomerAppReactNativeBuildNumber;
    private String bStackAndroidMidMileAppId;
    private String bStackAndroidMidMileAppBuildNumber;
    private String bStackAndroidFleetAppId;
    private String bStackAndroidFleetAppBuildNumber;
    private String bStackIosCustomerAppNativeAppId;
    private String bStackIosCustomerAppNativeAppBuildNumber;
    private String bStackIosCustomerAppReactNativeId;
    private String bStackIosCustomerAppReactNativeBuildNumber;
    private String bStackIosMidMileAppId;
    private String bStackIosMidMileAppBuildNumber;
    private String bStackIosFleetAppId;
    private String bStackIosFleetAppBuildNumber;
    private String lambdaTestUserName;
    private String lambdaTestAccessKey;
    private String lambdaTestVisualKey;
    private String lambdaTestNetworkKey;
    private String lambdaTestVideoKey;
    private String lambdaTestIsRealMobileKey;
    private String lambdaTestConsoleKey;
    private String lambdaTestAndroidCustomerAppNativeAppId;
    private String lambdaTestAndroidCustomerAppNativeAppBuildNumber;
    private String lambdaTestIosCustomerAppNativeAppId;
    private String lambdaTestIosCustomerAppNativeAppBuildNumber;
    private Boolean androidBuildEnabled;
    private Boolean webhookBuildEnabled;
    private Boolean iOSBuildEnabled;
    private String androidDeviceName;
    private String iOSDeviceName;
    private String androidPlatformName;
    private String androidPlatformVersion;
    private String androidADBName;
    private String androidAutomationName;
    private String iosPlatformName;
    private String iosPlatformVersion;
    private String iosUDID;
    private String iosAutomationName;
    private String adminLocalPhoneNumber;
    private String adminPhoneNumber;
    private String adminPhoneCountryCode;
    private String adminGmailAddress;
    private String adminGmailPassword;
    private String bypassScriptPassword;
    private String adminReferralCode;
    private boolean rolesTestsEnabled;
    private double testLatitude;
    private double testLongitude;
    private String testFpName;
    private String testFpDate;
    private String testOrderInfo;
    private String testWpSecCookieName;
    private String testWpLoggedInCookieName;
    private String testWpNodeAuthorizationCookieName;
    private boolean buildMobileCategoriesAndProductsTestData;
    private boolean buildMobileShopsTestData;
    private boolean buildWebControlRoomWarehousesAndOrders;
    private boolean registerUserUsingApi;
    private boolean buildMobileBillingTestData;
    private boolean cardServicesTestsEnabled;
    private boolean buildStockTakerTestData;
    private boolean buildFoodAggregatorTestData;
    private String cardAdminPanelAdminUserName;
    private String cardAdminPanelAdminPassword;
    private String cardUserMobileNumber;
    private String cardMobileLoginSchemeUserName;
    private String cardMobileLoginSchemePassword;
    private String cardLoginMobileUserPublicKey;
    private boolean testExecutionVideoRecordingEnabled;
    private String pickerPhoneNumber;
    private String pickerPhoneCountryCode;
    private String pickerPassword;
    private String midMilePhoneNumber;
    private String transitBaseURL;
    private String midMilePhoneCountryCode;
    private String midMilePassword;
    private String chefPhoneNumber;
    private String chefPassword;
    private String chefCountryCode;
    private String dAPhoneNumber;
    private String dAPhoneCountryCode;
    private String dAPassword;
    private String fpManagerCountryCode;
    private String fpManagerPhoneNumber;
    private String fpManagerPassword;
    private String targetApp;
    private String cardServiceContractNumber;
    private String cardServiceTypeId;
    private String cardServiceProductNumber;
    private String receiverMobileNumber;
    private String pickupLocationId;
    private String paymentServiceSecret;
    private String paymentShoppingKey;
    private String paymentTopUpKey;
    private String paymentBillingKey;
    private String paymentGratuityKey;
    private String inaiToken;
    private String stockTakeBaseURL;
    private String supplyChainStaticAuthToken;
    private boolean connectToDB;
    private String mysqlHost;
    private String mysqlUserName;
    private String mysqlUserPassword;
    private String mysqlDatabaseName;
    private int mysqlServerPort;
    private boolean sshConnectionRequired;
    private String sshHost;
    private String sshUserName;
    private int sshPortNumber;
    private boolean isSshKeyProtected;
    private String sshPassphrase;
    private String sshKeyPath;
    private String stockTakerPhoneNumber;
    private String stockTakerCountryCode;
    private String stockTakerPassword;
    private String defaultCardPasscode;
    private String updatedCardPasscode;
    private String paymentPanelEmail;
    private String paymentPanelPassword;
    private boolean useForeignCountryData;
    private String applePayInaiCallBackToken;
    private String imagePath;
    private String freshChatApiBaseUrl;
    private String freshChatApiKey;
    private String freshChatChannelId;
    private boolean skipAdminAuthorizationStep;
    private String orderServiceBaseURL;
    private boolean useOrderApiV2;
    private List<Integer> fallBackProductIds = new ArrayList<>();
    private int targetQtyForEachFallBackProduct;
    private List<Integer> excludedCategoryIds = new ArrayList<>();
    private String cardUserNationalIdExpiryDate;
    private String testMidMileOrdersDate;
    private List<AndroidDevice> androidDevicesPool = new ArrayList<>();
    private List<IosDevice> iosDevicesPool = new ArrayList<>();
    private String foodAggregatorServiceBaseURL;
    private String yeloMarketplaceUserId;
    private String yeloBaseURL;

    public String getChromeDriverPath() {
        return chromeDriverPath;
    }

    public void setChromeDriverPath(String chromeDriverPath) {
        this.chromeDriverPath = chromeDriverPath;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getBaseURL() {
        return baseURL;
    }

    public void setBaseURL(String baseURL) {
        this.baseURL = baseURL;
    }

    public String getControlRoomBaseURL() {
        return controlRoomBaseURL;
    }

    public void setControlRoomBaseURL(String controlRoomBaseURL) {
        this.controlRoomBaseURL = controlRoomBaseURL;
    }

    public String getBillingServicesBaseURL() {
        return billingServicesBaseURL;
    }

    public void setBillingServicesBaseURL(String billingServicesBaseURL) {
        this.billingServicesBaseURL = billingServicesBaseURL;
    }

    public String getCardServicesBaseURL() {
        return cardServicesBaseURL;
    }

    public void setCardServicesBaseURL(String cardServicesBaseURL) {
        this.cardServicesBaseURL = cardServicesBaseURL;
    }

    public String getCardServicesAdminPanelBaseURL() {
        return cardServicesAdminPanelBaseURL;
    }

    public void setCardServicesAdminPanelBaseURL(String cardServicesAdminPanelBaseURL) {
        this.cardServicesAdminPanelBaseURL = cardServicesAdminPanelBaseURL;
    }

    public String getPickerServicesBaseURL() {
        return pickerServicesBaseURL;
    }

    public void setPickerServicesBaseURL(String pickerServicesBaseURL) {
        this.pickerServicesBaseURL = pickerServicesBaseURL;
    }

    public String getTransitBaseURL() {
        return transitBaseURL;
    }
    public void setTransitBaseURL(String transitBaseURL) {
        this.transitBaseURL = transitBaseURL;
    }
    public String getPaymentServiceBaseURL(){ return paymentServiceBaseURL; }

    public void setPaymentServiceBaseURL(String paymentServiceBaseURL){
        this.paymentServiceBaseURL=paymentServiceBaseURL;
    }

    public String getInaiBaseURL(){ return inaiBaseURL;}

    public void setInaiBaseURL(String inaiBaseURL){
        this.inaiBaseURL=inaiBaseURL;
    }

    public String getOrderServiceBaseURL() {
        return orderServiceBaseURL;
    }

    public void setOrderServiceBaseURL(String orderServiceBaseURL) {
        this.orderServiceBaseURL = orderServiceBaseURL;
    }

    public String getFoodAggregatorServiceBaseURL() {
        return foodAggregatorServiceBaseURL;
    }

    public void setFoodAggregatorServiceBaseURL(String foodAggregatorServiceBaseURL) {
        this.foodAggregatorServiceBaseURL = foodAggregatorServiceBaseURL;
    }

    public String getTestEmail() {
        return testEmail;
    }

    public void setTestEmail(String testEmail) {
        this.testEmail = testEmail;
    }

    public String getTestPassword() {
        return testPassword;
    }

    public void setTestPassword(String testPassword) {
        this.testPassword = testPassword;
    }

    public String getTestCountryCode() {
        return testCountryCode;
    }

    public void setTestCountryCode(String countryCode) {
        this.testCountryCode = countryCode;
    }

    public String getTestMobileNumber() {
        return testMobileNumber;
    }

    public void setTestMobileNumber(String testMobileNumber) {
        this.testMobileNumber = testMobileNumber;
    }

    public String getTestCreditCard() {
        return testCreditCard;
    }

    public String getTestMobileCompany() {
        return testMobileCompany;
    }

    public void setTestMobileCompany(String testMobileCompany) {
        this.testMobileCompany = testMobileCompany;
    }

    public void setTestCreditCard(String testCreditCard) {
        this.testCreditCard = testCreditCard;
    }

    public String getTestExpDate() {
        return testExpDate;
    }

    public void setTestExpDate(String testExpDate) {
        this.testExpDate = testExpDate;
    }

    public String getTestCVC() {
        return testCVC;
    }

    public void setTestCVC(String testCVC) {
        this.testCVC = testCVC;
    }

    public String getSecondaryTestCreditCard() {
        return secondaryTestCreditCard;
    }

    public void setSecondaryTestCreditCard(String secondaryTestCreditCard) {
        this.secondaryTestCreditCard = secondaryTestCreditCard;
    }

    public String getSecondaryTestExpDate() {
        return secondaryTestExpDate;
    }

    public void setSecondaryTestExpDate(String secondaryTestExpDate) {
        this.secondaryTestExpDate = secondaryTestExpDate;
    }

    public String getSecondaryTestCVC() {
        return secondaryTestCVC;
    }

    public void setSecondaryTestCVC(String secondaryTestCVC) {
        this.secondaryTestCVC = secondaryTestCVC;
    }

    public String getWebRunMode() {
        return webRunMode;
    }

    public void setWebRunMode(String webRunMode) {
        this.webRunMode = webRunMode;
    }

    public String getMobileRunMode() {
        return mobileRunMode;
    }

    public void setMobileRunMode(String mobileRunMode) {
        this.mobileRunMode = mobileRunMode;
    }

    public String getGridURL() {
        return gridURL;
    }

    public void setGridURL(String gridURL) {
        this.gridURL = gridURL;
    }

    public String getInternalAPIToken() {
        return internalAPIToken;
    }

    public void setInternalAPIToken(String internalAPIToken) {
        this.internalAPIToken = internalAPIToken;
    }

    public String getNgrokAuthToken() {
        return ngrokAuthToken;
    }

    public void setNgrokAuthToken(String ngrokAuthToken) {
        this.ngrokAuthToken = ngrokAuthToken;
    }

    public String getSlackApiToken() {
        return slackApiToken;
    }

    public void setSlackApiToken(String slackApiToken) {
        this.slackApiToken = slackApiToken;
    }

    public String getSlackApiBaseURL() {
        return slackApiBaseURL;
    }

    public void setSlackApiBaseURL(String slackApiBaseURL) {
        this.slackApiBaseURL = slackApiBaseURL;
    }

    public Boolean getWebBuildEnabled() {
        return webBuildEnabled;
    }

    public void setWebBuildEnabled(Boolean webBuildEnabled) {
        this.webBuildEnabled = webBuildEnabled;
    }

    public Boolean getMobileBuildEnabled() {
        return mobileBuildEnabled;
    }

    public void setMobileBuildEnabled(Boolean mobileBuildEnabled) {
        this.mobileBuildEnabled = mobileBuildEnabled;
    }

    public Boolean getWebhookBuildEnabled() {
        return webhookBuildEnabled;
    }

    public void setWebhookBuildEnabled(Boolean webhookBuildEnabled) {
        this.webhookBuildEnabled = webhookBuildEnabled;
    }

    public Boolean getAndroidBuildEnabled() {
        return androidBuildEnabled;
    }

    public void setAndroidBuildEnabled(Boolean androidBuildEnabled) {
        this.androidBuildEnabled = androidBuildEnabled;
    }

    public Boolean getiOSBuildEnabled() {
        return iOSBuildEnabled;
    }

    public void setiOSBuildEnabled(Boolean iOSBuildEnabled) {
        this.iOSBuildEnabled = iOSBuildEnabled;
    }

    public String getAndroidDeviceName() {
        return androidDeviceName;
    }

    public void setAndroidDeviceName(String androidDeviceName) {
        this.androidDeviceName = androidDeviceName;
    }

    public String getiOSDeviceName() {
        return iOSDeviceName;
    }

    public void setiOSDeviceName(String iOSDeviceName) {
        this.iOSDeviceName = iOSDeviceName;
    }

    public String getAndroidPlatformName() {
        return androidPlatformName;
    }

    public void setAndroidPlatformName(String androidPlatformName) {
        this.androidPlatformName = androidPlatformName;
    }

    public String getAndroidPlatformVersion() {
        return androidPlatformVersion;
    }

    public void setAndroidPlatformVersion(String androidPlatformVersion) {
        this.androidPlatformVersion = androidPlatformVersion;
    }

    public String getAndroidADBName() {
        return androidADBName;
    }

    public void setAndroidADBName(String androidADBName) {
        this.androidADBName = androidADBName;
    }

    public String getAndroidAutomationName() {
        return androidAutomationName;
    }

    public void setAndroidAutomationName(String androidAutomationName) {
        this.androidAutomationName = androidAutomationName;
    }

    public String getIosPlatformName() {
        return iosPlatformName;
    }

    public void setIosPlatformName(String iosPlatformName) {
        this.iosPlatformName = iosPlatformName;
    }

    public String getIosPlatformVersion() {
        return iosPlatformVersion;
    }

    public void setIosPlatformVersion(String iosPlatformVersion) {
        this.iosPlatformVersion = iosPlatformVersion;
    }

    public String getIosUDID() {
        return iosUDID;
    }

    public void setIosUDID(String iosUDID) {
        this.iosUDID = iosUDID;
    }

    public String getIosAutomationName() {
        return iosAutomationName;
    }

    public void setIosAutomationName(String iosAutomationName) {
        this.iosAutomationName = iosAutomationName;
    }

    public String getAdminLocalPhoneNumber() {
        return adminLocalPhoneNumber;
    }

    public void setAdminLocalPhoneNumber(String adminLocalPhoneNumber) {
        this.adminLocalPhoneNumber = adminLocalPhoneNumber;
    }

    public String getAdminPhoneNumber() {
        return adminPhoneNumber;
    }

    public void setAdminPhoneNumber(String adminPhoneNumber) {
        this.adminPhoneNumber = adminPhoneNumber;
    }

    public String getAdminPhoneCountryCode() {
        return adminPhoneCountryCode;
    }

    public void setAdminPhoneCountryCode(String adminPhoneCountryCode) {
        this.adminPhoneCountryCode = adminPhoneCountryCode;
    }

    public String getAdminGmailAddress() {
        return adminGmailAddress;
    }

    public void setAdminGmailAddress(String adminGmailAddress) {
        this.adminGmailAddress = adminGmailAddress;
    }

    public String getAdminGmailPassword() {
        return adminGmailPassword;
    }

    public void setAdminGmailPassword(String adminGmailPassword) {
        this.adminGmailPassword = adminGmailPassword;
    }

    public String getBypassScriptPassword() {
        return bypassScriptPassword;
    }

    public void setBypassScriptPassword(String bypassScriptPassword) {
        this.bypassScriptPassword = bypassScriptPassword;
    }

    public String getAdminReferralCode() {
        return adminReferralCode;
    }

    public void setAdminReferralCode(String adminReferralCode) {
        this.adminReferralCode = adminReferralCode;
    }

    public boolean isRolesTestsEnabled() {
        return rolesTestsEnabled;
    }

    public void setRolesTestsEnabled(boolean rolesTestsEnabled) {
        this.rolesTestsEnabled = rolesTestsEnabled;
    }

    public double getTestLatitude() {
        return testLatitude;
    }

    public void setTestLatitude(double testLatitude) {
        this.testLatitude = testLatitude;
    }

    public double getTestLongitude() {
        return testLongitude;
    }

    public void setTestLongitude(double testLongitude) {
        this.testLongitude = testLongitude;
    }

    public String getTestFpName() {
        return testFpName;
    }

    public void setTestFpName(String testFpName) {
        this.testFpName = testFpName;
    }

    public String getTestFpDate() {
        return testFpDate;
    }

    public void setTestFpDate(String testFpDate) {
        this.testFpDate = testFpDate;
    }

    public String getTestOrderInfo() {
        return testOrderInfo;
    }

    public void setTestOrderInfo(String testOrderInfo) {
        this.testOrderInfo = testOrderInfo;
    }

    public String getTestWpSecCookieName() {
        return testWpSecCookieName;
    }

    public void setTestWpSecCookieName(String testWpSecCookieName) {
        this.testWpSecCookieName = testWpSecCookieName;
    }

    public String getTestWpLoggedInCookieName() {
        return testWpLoggedInCookieName;
    }

    public void setTestWpLoggedInCookieName(String testWpLoggedInCookieName) {
        this.testWpLoggedInCookieName = testWpLoggedInCookieName;
    }

    public String getTestWpNodeAuthorizationCookieName() {
        return testWpNodeAuthorizationCookieName;
    }

    public void setTestWpNodeAuthorizationCookieName(String testWpNodeAuthorizationCookieName) {
        this.testWpNodeAuthorizationCookieName = testWpNodeAuthorizationCookieName;
    }

    public boolean isBuildMobileCategoriesAndProductsTestData() {
        return buildMobileCategoriesAndProductsTestData;
    }

    public void setBuildMobileCategoriesAndProductsTestData(boolean buildMobileCategoriesAndProductsTestData) {
        this.buildMobileCategoriesAndProductsTestData = buildMobileCategoriesAndProductsTestData;
    }

    public boolean isBuildMobileShopsTestData() {
        return buildMobileShopsTestData;
    }

    public void setBuildMobileShopsTestData(boolean buildMobileShopsTestData) {
        this.buildMobileShopsTestData = buildMobileShopsTestData;
    }

    public boolean isBuildWebControlRoomWarehousesAndOrders() {
        return buildWebControlRoomWarehousesAndOrders;
    }

    public void setBuildWebControlRoomWarehousesAndOrders(boolean buildWebControlRoomWarehousesAndOrders) {
        this.buildWebControlRoomWarehousesAndOrders = buildWebControlRoomWarehousesAndOrders;
    }

    public boolean isRegisterUserUsingApi() {
        return registerUserUsingApi;
    }

    public void setRegisterUserUsingApi(boolean registerUserUsingApi) {
        this.registerUserUsingApi = registerUserUsingApi;
    }

    public boolean isBuildMobileBillingTestData() {
        return buildMobileBillingTestData;
    }

    public void setBuildMobileBillingTestData(boolean buildMobileBillingTestData) {
        this.buildMobileBillingTestData = buildMobileBillingTestData;
    }

    public boolean isCardServicesTestsEnabled() {
        return cardServicesTestsEnabled;
    }

    public void setCardServicesTestsEnabled(boolean cardServicesTestsEnabled) {
        this.cardServicesTestsEnabled = cardServicesTestsEnabled;
    }

    public boolean isBuildStockTakerTestData() {
        return buildStockTakerTestData;
    }

    public void setBuildStockTakerTestData(boolean buildStockTakerTestData) {
        this.buildStockTakerTestData = buildStockTakerTestData;
    }

    public boolean isBuildFoodAggregatorTestData() {return buildFoodAggregatorTestData;}

    public void setBuildFoodAggregatorTestData(boolean buildFoodAggregatorTestData) {
        this.buildFoodAggregatorTestData = buildFoodAggregatorTestData;}

    public String getCardAdminPanelAdminUserName() {
        return cardAdminPanelAdminUserName;
    }

    public void setCardAdminPanelAdminUserName(String cardAdminPanelAdminUserName) {
        this.cardAdminPanelAdminUserName = cardAdminPanelAdminUserName;
    }

    public String getCardAdminPanelAdminPassword() {
        return cardAdminPanelAdminPassword;
    }

    public void setCardAdminPanelAdminPassword(String cardAdminPanelAdminPassword) {
        this.cardAdminPanelAdminPassword = cardAdminPanelAdminPassword;
    }
    public String getCardUserMobileNumber() {
        return cardUserMobileNumber;
    }
    public void setCardUserMobileNumber(String cardUserMobileNumber) {
        this.cardUserMobileNumber =cardUserMobileNumber;
    }
    public String getPickupLocationId() {
        return pickupLocationId;
    }
    public void setPickupLocationId(String pickupLocationId) {
        this.pickupLocationId = pickupLocationId;
    }

    public String getCardMobileLoginSchemeUserName() {
        return cardMobileLoginSchemeUserName;
    }

    public void setCardMobileLoginSchemeUserName(String cardMobileLoginSchemeUserName) {
        this.cardMobileLoginSchemeUserName = cardMobileLoginSchemeUserName;
    }

    public String getCardMobileLoginSchemePassword() {
        return cardMobileLoginSchemePassword;
    }

    public void setCardMobileLoginSchemePassword(String cardMobileLoginSchemePassword) {
        this.cardMobileLoginSchemePassword = cardMobileLoginSchemePassword;
    }

    public String getCardLoginMobileUserPublicKey() {
        return cardLoginMobileUserPublicKey;
    }

    public void setCardLoginMobileUserPublicKey(String cardLoginMobileUserPublicKey) {
        this.cardLoginMobileUserPublicKey = cardLoginMobileUserPublicKey;
    }

    public boolean isTestExecutionVideoRecordingEnabled() {
        return testExecutionVideoRecordingEnabled;
    }

    public void setTestExecutionVideoRecordingEnabled(boolean testExecutionVideoRecordingEnabled) {
        this.testExecutionVideoRecordingEnabled = testExecutionVideoRecordingEnabled;
    }

    public String getPickerPhoneNumber() {
        return pickerPhoneNumber;
    }

    public void setPickerPhoneNumber(String pickerPhoneNumber) {
        this.pickerPhoneNumber = pickerPhoneNumber;
    }

    public String getPickerPhoneCountryCode() {
        return pickerPhoneCountryCode;
    }

    public void setPickerPhoneCountryCode(String pickerPhoneCountryCode) {
        this.pickerPhoneCountryCode = pickerPhoneCountryCode;
    }

    public String getPickerPassword() {
        return pickerPassword;
    }

    public void setPickerPassword(String pickerPassword) {
        this.pickerPassword = pickerPassword;
    }

    public String getMidMileAppBaseURL() {
        return midMileAppBaseURL;
    }

    public void setMidMileAppBaseURL(String midMileAppBaseURL) {
        this.midMileAppBaseURL = midMileAppBaseURL;
    }
    public String getLogisticsBaseURL() {
        return logisticsBaseURL;
    }

    public String getFleetAppBaseURL() {
        return fleetAppBaseURL;
    }

    public void setFleetAppBaseURL(String fleetAppBaseURL) {
        this.fleetAppBaseURL = fleetAppBaseURL;
    }

    public void setLogisticsBaseURL(String logisticsBaseURL) {
        this.logisticsBaseURL = logisticsBaseURL;
    }

    public String getMidMilePhoneNumber() {
        return midMilePhoneNumber;
    }

    public void setMidMilePhoneNumber(String midMilePhoneNumber) {
        this.midMilePhoneNumber = midMilePhoneNumber;
    }

    public String getMidMilePhoneCountryCode() {
        return midMilePhoneCountryCode;
    }

    public void setMidMilePhoneCountryCode(String midMilePhoneCountryCode) {
        this.midMilePhoneCountryCode = midMilePhoneCountryCode;
    }

    public String getMidMilePassword() {
        return midMilePassword;
    }

    public void setMidMilePassword(String midMilePassword) {
        this.midMilePassword = midMilePassword;
    }

    public String getChefPhoneNumber() {
        return chefPhoneNumber;
    }

    public void setChefPhoneNumber(String chefPhoneNumber) {
        this.chefPhoneNumber = chefPhoneNumber;
    }

    public String getChefPassword() {
        return chefPassword;
    }

    public void setChefPassword(String chefPassword) {
        this.chefPassword = chefPassword;
    }

    public String getChefCountryCode() {
        return chefCountryCode;
    }

    public void setChefCountryCode(String chefCountryCode) {
        this.chefCountryCode = chefCountryCode;
    }
    public String getdAPhoneNumber() {
        return dAPhoneNumber;
    }

    public void setdAPhoneNumber(String dAPhoneNumber) {
        this.dAPhoneNumber = dAPhoneNumber;
    }

    public String getdAPhoneCountryCode() {
        return dAPhoneCountryCode;
    }

    public void setdAPhoneCountryCode(String dAPhoneCountryCode) {
        this.dAPhoneCountryCode = dAPhoneCountryCode;
    }

    public String getdAPassword() {
        return dAPassword;
    }

    public void setdAPassword(String dAPassword) {
        this.dAPassword = dAPassword;
    }

    public String getFpManagerCountryCode() {
        return fpManagerCountryCode;
    }

    public void setFpManagerCountryCode(String fpManagerCountryCode) {
        this.fpManagerCountryCode = fpManagerCountryCode;
    }

    public String getFpManagerPhoneNumber() {
        return fpManagerPhoneNumber;
    }

    public void setFpManagerPhoneNumber(String fpManagerPhoneNumber) {
        this.fpManagerPhoneNumber = fpManagerPhoneNumber;
    }

    public String getFpManagerPassword() {
        return fpManagerPassword;
    }

    public void setFpManagerPassword(String fpManagerPassword) {
        this.fpManagerPassword = fpManagerPassword;
    }

    public String getTargetApp() {
        return targetApp;
    }

    public void setTargetApp(String targetApp) {
        this.targetApp = targetApp;
    }

    public String getCardServiceContractNumber() { return cardServiceContractNumber; }

    public void setCardServiceContractNumber(String cardServiceContractNumber) { this.cardServiceContractNumber = cardServiceContractNumber; }

    public String getCardServiceTypeId() { return cardServiceTypeId; }

    public void setCardServiceTypeId(String cardServiceTypeId) { this.cardServiceTypeId = cardServiceTypeId; }

    public String getCardServiceProductNumber() { return cardServiceProductNumber; }

    public void setCardServiceProductNumber(String cardServiceProductNumber) { this.cardServiceProductNumber = cardServiceProductNumber; }

    public String getReceiverMobileNumber() { return receiverMobileNumber; }

    public void setReceiverMobileNumber(String receiverMobileNumber) { this.receiverMobileNumber = receiverMobileNumber; }

    public String getPaymentServiceSecret(){ return paymentServiceSecret;}

    public void setPaymentServiceSecret(String paymentServiceSecret){ this.paymentServiceSecret = paymentServiceSecret;}

    public String getPaymentShoppingKey(){ return paymentShoppingKey;}

    public void setPaymentShoppingKey(String paymentShoppingKey){ this.paymentShoppingKey = paymentShoppingKey;}

    public String getPaymentTopUpKey(){ return paymentTopUpKey;}

    public void setPaymentTopUpKey(String paymentTopUpKey){ this.paymentTopUpKey = paymentTopUpKey;}

    public String getPaymentBillingKey(){ return paymentBillingKey;}

    public void setPaymentBillingKey(String paymentBillingKey){ this.paymentBillingKey = paymentBillingKey;}

    public String getPaymentGratuityKey(){ return paymentGratuityKey;}

    public void setPaymentGratuityKey(String paymentGratuityKey){ this.paymentGratuityKey = paymentGratuityKey;}

    public String getInaiToken(){ return inaiToken;}

    public void setInaiToken(String inaiToken){ this.inaiToken = inaiToken;}

    public String getStockTakeBaseURL() {
        return stockTakeBaseURL;
    }

    public void setStockTakeBaseURL(String stocktakeBaseURL) {
        this.stockTakeBaseURL = stocktakeBaseURL;
    }

    public String getSupplyChainStaticAuthToken() {
        return supplyChainStaticAuthToken;
    }

    public void setSupplyChainStaticAuthToken(String supplyChainStaticAuthToken) {
        this.supplyChainStaticAuthToken = supplyChainStaticAuthToken;
    }

    public boolean isConnectToDB() {
        return connectToDB;
    }

    public void setConnectToDB(boolean connectToDB) {
        this.connectToDB = connectToDB;
    }

    public String getMysqlHost() {
        return mysqlHost;
    }

    public void setMysqlHost(String mysqlHost) {
        this.mysqlHost = mysqlHost;
    }

    public String getMysqlUserName() {
        return mysqlUserName;
    }

    public void setMysqlUserName(String mysqlUserName) {
        this.mysqlUserName = mysqlUserName;
    }

    public String getMysqlUserPassword() {
        return mysqlUserPassword;
    }

    public void setMysqlUserPassword(String mysqlUserPassword) {
        this.mysqlUserPassword = mysqlUserPassword;
    }

    public String getMysqlDatabaseName() {
        return mysqlDatabaseName;
    }

    public void setMysqlDatabaseName(String mysqlDatabaseName) {
        this.mysqlDatabaseName = mysqlDatabaseName;
    }

    public int getMysqlServerPort() {
        return mysqlServerPort;
    }

    public void setMysqlServerPort(int mysqlServerPort) {
        this.mysqlServerPort = mysqlServerPort;
    }

    public boolean isSshConnectionRequired() {
        return sshConnectionRequired;
    }

    public void setSshConnectionRequired(boolean sshConnectionRequired) {
        this.sshConnectionRequired = sshConnectionRequired;
    }

    public String getSshHost() {
        return sshHost;
    }

    public void setSshHost(String sshHost) {
        this.sshHost = sshHost;
    }

    public String getSshUserName() {
        return sshUserName;
    }

    public void setSshUserName(String sshUserName) {
        this.sshUserName = sshUserName;
    }

    public int getSshPortNumber() {
        return sshPortNumber;
    }

    public void setSshPortNumber(int sshPortNumber) {
        this.sshPortNumber = sshPortNumber;
    }

    public boolean isSshKeyProtected() {
        return isSshKeyProtected;
    }

    public void setSshKeyProtected(boolean sshKeyProtected) {
        isSshKeyProtected = sshKeyProtected;
    }

    public String getSshPassphrase() {
        return sshPassphrase;
    }

    public void setSshPassphrase(String sshPassphrase) {
        this.sshPassphrase = sshPassphrase;
    }

    public String getSshKeyPath() {
        return sshKeyPath;
    }

    public void setSshKeyPath(String sshKeyPath) {
        this.sshKeyPath = sshKeyPath;
    }

    public String getFleetServiceBaseURL() {
        return fleetServiceBaseURL;
    }

    public void setFleetServiceBaseURL(String fleetServiceBaseURL) {
        this.fleetServiceBaseURL = fleetServiceBaseURL;
    }

    public String getStockTakerPhoneNumber() {
        return stockTakerPhoneNumber;
    }

    public void setStockTakerPhoneNumber(String stockTakerPhoneNumber) {
        this.stockTakerPhoneNumber = stockTakerPhoneNumber;
    }

    public String getStockTakerCountryCode() {
        return stockTakerCountryCode;
    }

    public void setStockTakerCountryCode(String stockTakerCountryCode) {
        this.stockTakerCountryCode = stockTakerCountryCode;
    }

    public String getStockTakerPassword() {
        return stockTakerPassword;
    }

    public void setStockTakerPassword(String stockTakerPassword) {
        this.stockTakerPassword = stockTakerPassword;
    }

    public String getDefaultCardPasscode() {
        return defaultCardPasscode;
    }

    public void setDefaultCardPasscode(String defaultCardPasscode) {
        this.defaultCardPasscode = defaultCardPasscode;
    }
    public String getUpdatedCardPasscode() {
        return updatedCardPasscode;
    }
    public void setUpdatedCardPasscode(String updatedCardPasscode) {
        this.updatedCardPasscode = updatedCardPasscode;
    }
    public String getPaymentPanelEmail() {
        return paymentPanelEmail;
    }

    public void setPaymentPanelEmail(String paymentPanelEmail) {
        this.paymentPanelEmail = paymentPanelEmail;
    }

    public String getPaymentPanelPassword() {
        return paymentPanelPassword;
    }

    public void setPaymentPanelPassword(String paymentPanelPassword) {
        this.paymentPanelPassword = paymentPanelPassword;
    }

    public boolean isUseForeignCountryData() {
        return useForeignCountryData;
    }

    public void setUseForeignCountryData(boolean useForeignCountryData) {
        this.useForeignCountryData = useForeignCountryData;
    }
    public String getApplePayInaiCallBackToken ()
    {return applePayInaiCallBackToken;}

    public void setApplePayInaiCallBackToken(String applePayInaiCallBackToken)
    { this.applePayInaiCallBackToken= applePayInaiCallBackToken;}

    public String getInternalOrdersBaseURL() {
        return internalOrdersBaseURL;
    }

    public void setInternalOrdersBaseURL(String internalOrdersBaseURL) {
        this.internalOrdersBaseURL = internalOrdersBaseURL;
    }

    public String getBrowserStackBaseURL() {
        return browserStackBaseURL;
    }

    public void setBrowserStackBaseURL(String browserStackBaseURL) {
        this.browserStackBaseURL = browserStackBaseURL;
    }

    public String getCatalogBaseURL() {
        return catalogBaseURL;
    }

    public void setCatalogBaseURL(String catalogBaseURL) {
        this.catalogBaseURL = catalogBaseURL;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getFreshChatApiBaseUrl() {
        return freshChatApiBaseUrl;
    }

    public void setFreshChatApiBaseUrl(String freshChatApiBaseUrl) {
        this.freshChatApiBaseUrl = freshChatApiBaseUrl;
    }

    public String getFreshChatApiKey() {
        return freshChatApiKey;
    }

    public void setFreshChatApiKey(String freshChatApiKey) {
        this.freshChatApiKey = freshChatApiKey;
    }

    public String getFreshChatChannelId() {
        return freshChatChannelId;
    }

    public void setFreshChatChannelId(String freshChatChannelId) {
        this.freshChatChannelId = freshChatChannelId;
    }

    public boolean isSkipAdminAuthorizationStep() {
        return skipAdminAuthorizationStep;
    }

    public void setSkipAdminAuthorizationStep(boolean skipAdminAuthorizationStep) {
        this.skipAdminAuthorizationStep = skipAdminAuthorizationStep;
    }

    public boolean isUseOrderApiV2() {
        return useOrderApiV2;
    }

    public void setUseOrderApiV2(boolean useOrderApiV2) {
        this.useOrderApiV2 = useOrderApiV2;
    }

    public String getRemoteProviderName() {
        return remoteProviderName;
    }

    public void setRemoteProviderName(String remoteProviderName) {
        this.remoteProviderName = remoteProviderName;
    }

    public String getBStackUserName() {
        return bStackUserName;
    }

    public void setBStackUserName(String bStackUserName) {
        this.bStackUserName = bStackUserName;
    }

    public String getBStackAccessKey() {
        return bStackAccessKey;
    }

    public void setBStackAccessKey(String bStackAccessKey) {
        this.bStackAccessKey = bStackAccessKey;
    }

    public String getBStackLocal() {
        return bStackLocal;
    }

    public void setBStackLocal(String bStackLocal) {
        this.bStackLocal = bStackLocal;
    }

    public String getBStackDebug() {
        return bStackDebug;
    }

    public void setBStackDebug(String bStackDebug) {
        this.bStackDebug = bStackDebug;
    }

    public String getBStackNetworkLogs() {
        return bStackNetworkLogs;
    }

    public void setBStackNetworkLogs(String bStackNetworkLogs) {
        this.bStackNetworkLogs = bStackNetworkLogs;
    }

    public String getBStackSelfHeal() {
        return bStackSelfHeal;
    }

    public void setBStackSelfHeal(String bStackSelfHeal) {
        this.bStackSelfHeal = bStackSelfHeal;
    }

    public String getbStackAppiumVersionForAndroidTests() {
        return bStackAppiumVersionForAndroidTests;
    }

    public void setbStackAppiumVersionForAndroidTests(String bStackAppiumVersionForAndroidTests) {
        this.bStackAppiumVersionForAndroidTests = bStackAppiumVersionForAndroidTests;
    }

    public String getbStackAppiumVersionForIosTests() {
        return bStackAppiumVersionForIosTests;
    }

    public void setbStackAppiumVersionForIosTests(String bStackAppiumVersionForIosTests) {
        this.bStackAppiumVersionForIosTests = bStackAppiumVersionForIosTests;
    }

    public String getbStackAndroidCustomerAppNativeAppId() {
        return bStackAndroidCustomerAppNativeAppId;
    }

    public void setbStackAndroidCustomerAppNativeAppId(String bStackAndroidCustomerAppNativeAppId) {
        this.bStackAndroidCustomerAppNativeAppId = bStackAndroidCustomerAppNativeAppId;
    }

    public String getbStackAndroidCustomerAppReactNativeId() {
        return bStackAndroidCustomerAppReactNativeId;
    }

    public void setbStackAndroidCustomerAppReactNativeId(String bStackAndroidCustomerAppReactNativeId) {
        this.bStackAndroidCustomerAppReactNativeId = bStackAndroidCustomerAppReactNativeId;
    }

    public String getbStackAndroidMidMileAppId() {
        return bStackAndroidMidMileAppId;
    }

    public void setbStackAndroidMidMileAppId(String bStackAndroidMidMileAppId) {
        this.bStackAndroidMidMileAppId = bStackAndroidMidMileAppId;
    }

    public String getbStackAndroidFleetAppId() {
        return bStackAndroidFleetAppId;
    }

    public void setbStackAndroidFleetAppId(String bStackAndroidFleetAppId) {
        this.bStackAndroidFleetAppId = bStackAndroidFleetAppId;
    }

    public String getbStackIosCustomerAppNativeAppId() {
        return bStackIosCustomerAppNativeAppId;
    }

    public void setbStackIosCustomerAppNativeAppId(String bStackIosCustomerAppNativeAppId) {
        this.bStackIosCustomerAppNativeAppId = bStackIosCustomerAppNativeAppId;
    }

    public String getbStackIosCustomerAppReactNativeId() {
        return bStackIosCustomerAppReactNativeId;
    }

    public void setbStackIosCustomerAppReactNativeId(String bStackIosCustomerAppReactNativeId) {
        this.bStackIosCustomerAppReactNativeId = bStackIosCustomerAppReactNativeId;
    }

    public String getbStackIosMidMileAppId() {
        return bStackIosMidMileAppId;
    }

    public void setbStackIosMidMileAppId(String bStackIosMidMileAppId) {
        this.bStackIosMidMileAppId = bStackIosMidMileAppId;
    }

    public String getbStackIosFleetAppId() {
        return bStackIosFleetAppId;
    }

    public void setbStackIosFleetAppId(String bStackIosFleetAppId) {
        this.bStackIosFleetAppId = bStackIosFleetAppId;
    }

    public String getbStackAndroidCustomerAppNativeAppBuildNumber() {
        return bStackAndroidCustomerAppNativeAppBuildNumber;
    }

    public void setbStackAndroidCustomerAppNativeAppBuildNumber(String bStackAndroidCustomerAppNativeAppBuildNumber) {
        this.bStackAndroidCustomerAppNativeAppBuildNumber = bStackAndroidCustomerAppNativeAppBuildNumber;
    }

    public String getbStackAndroidCustomerAppReactNativeBuildNumber() {
        return bStackAndroidCustomerAppReactNativeBuildNumber;
    }

    public void setbStackAndroidCustomerAppReactNativeBuildNumber(String bStackAndroidCustomerAppReactNativeBuildNumber) {
        this.bStackAndroidCustomerAppReactNativeBuildNumber = bStackAndroidCustomerAppReactNativeBuildNumber;
    }

    public String getbStackAndroidMidMileAppBuildNumber() {
        return bStackAndroidMidMileAppBuildNumber;
    }

    public void setbStackAndroidMidMileAppBuildNumber(String bStackAndroidMidMileAppBuildNumber) {
        this.bStackAndroidMidMileAppBuildNumber = bStackAndroidMidMileAppBuildNumber;
    }

    public String getbStackAndroidFleetAppBuildNumber() {
        return bStackAndroidFleetAppBuildNumber;
    }

    public void setbStackAndroidFleetAppBuildNumber(String bStackAndroidFleetAppBuildNumber) {
        this.bStackAndroidFleetAppBuildNumber = bStackAndroidFleetAppBuildNumber;
    }

    public String getbStackIosCustomerAppNativeAppBuildNumber() {
        return bStackIosCustomerAppNativeAppBuildNumber;
    }

    public void setbStackIosCustomerAppNativeAppBuildNumber(String bStackIosCustomerAppNativeAppBuildNumber) {
        this.bStackIosCustomerAppNativeAppBuildNumber = bStackIosCustomerAppNativeAppBuildNumber;
    }

    public String getbStackIosCustomerAppReactNativeBuildNumber() {
        return bStackIosCustomerAppReactNativeBuildNumber;
    }

    public void setbStackIosCustomerAppReactNativeBuildNumber(String bStackIosCustomerAppReactNativeBuildNumber) {
        this.bStackIosCustomerAppReactNativeBuildNumber = bStackIosCustomerAppReactNativeBuildNumber;
    }

    public String getbStackIosMidMileAppBuildNumber() {
        return bStackIosMidMileAppBuildNumber;
    }

    public void setbStackIosMidMileAppBuildNumber(String bStackIosMidMileAppBuildNumber) {
        this.bStackIosMidMileAppBuildNumber = bStackIosMidMileAppBuildNumber;
    }

    public String getbStackIosFleetAppBuildNumber() {
        return bStackIosFleetAppBuildNumber;
    }

    public void setbStackIosFleetAppBuildNumber(String bStackIosFleetAppBuildNumber) {
        this.bStackIosFleetAppBuildNumber = bStackIosFleetAppBuildNumber;
    }

    public String getLambdaTestUserName() {
        return lambdaTestUserName;
    }

    public void setLambdaTestUserName(String lambdaTestUserName) {
        this.lambdaTestUserName = lambdaTestUserName;
    }

    public String getLambdaTestAccessKey() {
        return lambdaTestAccessKey;
    }

    public void setLambdaTestAccessKey(String lambdaTestAccessKey) {
        this.lambdaTestAccessKey = lambdaTestAccessKey;
    }

    public String getLambdaTestVisualKey() {
        return lambdaTestVisualKey;
    }

    public void setLambdaTestVisualKey(String lambdaTestVisualKey) {
        this.lambdaTestVisualKey = lambdaTestVisualKey;
    }

    public String getLambdaTestNetworkKey() {
        return lambdaTestNetworkKey;
    }

    public void setLambdaTestNetworkKey(String lambdaTestNetworkKey) {
        this.lambdaTestNetworkKey = lambdaTestNetworkKey;
    }

    public String getLambdaTestVideoKey() {
        return lambdaTestVideoKey;
    }

    public void setLambdaTestVideoKey(String lambdaTestVideoKey) {
        this.lambdaTestVideoKey = lambdaTestVideoKey;
    }

    public String getLambdaTestIsRealMobileKey() {
        return lambdaTestIsRealMobileKey;
    }

    public void setLambdaTestIsRealMobileKey(String lambdaTestIsRealMobileKey) {
        this.lambdaTestIsRealMobileKey = lambdaTestIsRealMobileKey;
    }

    public String getLambdaTestConsoleKey() {
        return lambdaTestConsoleKey;
    }

    public void setLambdaTestConsoleKey(String lambdaTestConsoleKey) {
        this.lambdaTestConsoleKey = lambdaTestConsoleKey;
    }

    public String getLambdaTestAndroidCustomerAppNativeAppId() {
        return lambdaTestAndroidCustomerAppNativeAppId;
    }

    public void setLambdaTestAndroidCustomerAppNativeAppId(String lambdaTestAndroidCustomerAppNativeAppId) {
        this.lambdaTestAndroidCustomerAppNativeAppId = lambdaTestAndroidCustomerAppNativeAppId;
    }

    public String getLambdaTestAndroidCustomerAppNativeAppBuildNumber() {
        return lambdaTestAndroidCustomerAppNativeAppBuildNumber;
    }

    public void setLambdaTestAndroidCustomerAppNativeAppBuildNumber(String lambdaTestAndroidCustomerAppNativeAppBuildNumber) {
        this.lambdaTestAndroidCustomerAppNativeAppBuildNumber = lambdaTestAndroidCustomerAppNativeAppBuildNumber;
    }

    public String getLambdaTestIosCustomerAppNativeAppId() {
        return lambdaTestIosCustomerAppNativeAppId;
    }

    public void setLambdaTestIosCustomerAppNativeAppId(String lambdaTestIosCustomerAppNativeAppId) {
        this.lambdaTestIosCustomerAppNativeAppId = lambdaTestIosCustomerAppNativeAppId;
    }

    public String getLambdaTestIosCustomerAppNativeAppBuildNumber() {
        return lambdaTestIosCustomerAppNativeAppBuildNumber;
    }

    public void setLambdaTestIosCustomerAppNativeAppBuildNumber(String lambdaTestIosCustomerAppNativeAppBuildNumber) {
        this.lambdaTestIosCustomerAppNativeAppBuildNumber = lambdaTestIosCustomerAppNativeAppBuildNumber;
    }

    public List<Integer> getFallBackProductIds() {
        return fallBackProductIds;
    }

    public void setFallBackProductIds(List<Integer> fallBackProductIds) {
        this.fallBackProductIds = fallBackProductIds;
    }

    public int getTargetQtyForEachFallBackProduct() {
        return targetQtyForEachFallBackProduct;
    }

    public void setTargetQtyForEachFallBackProduct(int targetQtyForEachFallBackProduct) {
        this.targetQtyForEachFallBackProduct = targetQtyForEachFallBackProduct;
    }

    public List<Integer> getExcludedCategoryIds() {
        return excludedCategoryIds;
    }

    public void setExcludedCategoryIds(List<Integer> excludedCategoryIds) {
        this.excludedCategoryIds = excludedCategoryIds;
    }

    public String getCardUserNationalIdExpiryDate() {
        return cardUserNationalIdExpiryDate;
    }

    public void setCardUserNationalIdExpiryDate(String cardUserNationalIdExpiryDate) {
        this.cardUserNationalIdExpiryDate = cardUserNationalIdExpiryDate;
    }

    public String getDeclinedCreditCard() {
        return declinedCreditCard;
    }

    public void setDeclinedCreditCard(String declinedCreditCard) {
        this.declinedCreditCard = declinedCreditCard;
    }

    public String getTestMidMileOrdersDate() {
        return testMidMileOrdersDate;
    }

    public void setTestMidMileOrdersDate(String testMidMileOrdersDate) {
        this.testMidMileOrdersDate = testMidMileOrdersDate;
    }

    public List<AndroidDevice> getAndroidDevicesPool() {
        return androidDevicesPool;
    }

    public void setAndroidDevicesPool(List<AndroidDevice> androidDevicesPool) {
        this.androidDevicesPool = androidDevicesPool;
    }

    public List<IosDevice> getIosDevicesPool() {
        return iosDevicesPool;
    }

    public void setIosDevicesPool(List<IosDevice> iosDevicesPool) {
        this.iosDevicesPool = iosDevicesPool;
    }

    public String getYeloMarketplaceUserId() {return yeloMarketplaceUserId;}

    public void setYeloMarketplaceUserId(String yeloMarketplaceUserId) {this.yeloMarketplaceUserId = yeloMarketplaceUserId;}

    public String getYeloBaseURL() {return yeloBaseURL;}

    public void setYeloBaseURL(String yeloBaseURL) {this.yeloBaseURL = yeloBaseURL;}

}
