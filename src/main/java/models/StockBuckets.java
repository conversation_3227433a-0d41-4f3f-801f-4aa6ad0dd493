package models;

public class StockBuckets {
    private double onApp;
    private double reserved;
    private double fpStock;
    private double notSellable;
    private double missing;
    private double totalLiability;

    public double getOnApp() {
        return onApp;
    }

    public void setOnApp(double onApp) {
        this.onApp = onApp;
    }

    public double getReserved() {
        return reserved;
    }

    public void setReserved(double reserved) {
        this.reserved = reserved;
    }

    public double getFpStock() {
        return fpStock;
    }

    public void setFpStock(double fpStock) {
        this.fpStock = fpStock;
    }

    public double getNotSellable() {
        return notSellable;
    }

    public void setNotSellable(double notSellable) {
        this.notSellable = notSellable;
    }

    public double getMissing() {
        return missing;
    }

    public void setMissing(double missing) {
        this.missing = missing;
    }

    public double getTotalLiability() {
        return totalLiability;
    }

    public void setTotalLiability(double totalLiability) {
        this.totalLiability = totalLiability;
    }
}
