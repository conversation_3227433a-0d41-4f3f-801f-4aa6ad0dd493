package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.CouponDataParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.Coupon;
import models.Product;
import models.User;
import org.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class CouponsApiClient extends BaseHelper {
    Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(CouponsApiClient.class);

    private final String addCouponEndpoint = "/wp-json/coupons-api/v1/coupons";

    public CouponsApiClient(Configs configs) {
        this.configs = configs;
    }

    /**
     * Creates a coupon using the provided coupon code, discount type, theme, and other specified parameters.
     * This method is designed for use by administrators to generate coupons with custom configurations.
     *
     * @param adminUser        The admin user object, required for authorization purposes.
     * @param couponCode       The unique code for the new coupon.
     * @param discountType     The type of discount the coupon applies. Valid values are ("percent", "percentage", "%", "fixed", "free_delivery", "free delivery", "back_to_wallet", "back to wallet")
     * @param couponTheme      The theme or category this coupon belongs to. Valid values are "commercial"
     * @param amount           The discount amount. This could represent a percentage or a fixed discount amount, based on the discount type. Importantly noted, this value can be used for Back To Wallet Percentage coupon type for percent value.
     * @param maxAmount        The max amount for maximum value to be deducted in percentage cart only.
     * @param percentValue     The percent value for percentage cart only.
     * @param activationStatus Indicates the coupon's activation status. Valid values are (active, expired).
     * @param isActive         A boolean indicating if the coupon is currently active. Valid values are (true, false)
     * @param orderType        Specifies the type of orders the coupon applies to. Valid values are (tomorrow, later, now, both)
     * @param isPercent        A boolean indicating if the back to wallet coupon is a percentage or a fixed discount amount. Valid values are (true, false)
     * @return Coupon A Coupon object representing the newly created coupon with its properties set as per the arguments.
     */
    public Coupon createCouponUsingCouponCode(User adminUser,
                                              String couponCode,
                                              String discountType,
                                              String couponTheme,
                                              int amount,
                                              int maxAmount,
                                              int percentValue,
                                              String activationStatus,
                                              boolean isActive,
                                              boolean isPercent,
                                              String orderType) {
        logger.info("Starting to create a coupon with the following characteristics:"
                + "\n Coupon Code: " + couponCode
                + "\n Discount Type: " + discountType
                + "\n Coupon Theme: " + couponTheme
                + "\n Amount: " + amount
                + "\n Amount: " + maxAmount
                + "\n Amount: " + percentValue
                + "\n Activation Status: " + activationStatus
                + "\n Is Active: " + isActive
                + "\n Is Percent: " + isPercent
                + "\n Order Type: " + orderType);

        String requestBody = "{\n" +
                "    \"type\": \"" + getDiscountType(discountType) + "\",\n" +
                "    \"code\": \"" + couponCode + "\",\n" +
                "    \"active\":" + isActive + ",\n" +
                "    \"coupon_theme\": \"" + getCouponTheme(couponTheme) + "\",\n" +
                "    \"constrains\": {\n" +
                "        \"general\": {\n" +
                "            \"start_date\": \"" + getStartDate() + "\",\n" +
                "            \"end_date\": \"" + getEndDate(activationStatus) + "\",\n" +
                "            \"order_type\": \"" + getOrderType(orderType) + "\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"type_info\": {\n" +
                "        \"amount\":" + amount + ",\n" +
                "        \"max_amount\":" + maxAmount + ",\n" +
                "        \"percent_value\":" + percentValue + ",\n" +
                "        \"isPercent\":" + isPercent + "\n" +
                "    }\n" +
                "}";

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getBaseURL() + addCouponEndpoint)
                .then()
                .statusCode(201)
                .extract().response();

        JSONObject couponObject = new JSONObject(response.getBody().asString());
        Coupon parsedCoupon = new CouponDataParser().parseCouponJsonObject(couponObject);

        if (couponObject.has("id")) {
            logger.info("Successfully created a coupon with code \""
                    + parsedCoupon.getCouponCode()
                    + "\" and id \"" + parsedCoupon.getId() + "\".");
        } else {
            logger.error("Coupon code didn't get created and couldn't find an id in the response below:");
            logger.error(response.getBody().asString());
        }
        return parsedCoupon;
    }

    /**
     * @param adminUser        The admin user object, required for authorization purposes.
     * @param couponCode       The unique code for the new coupon.
     * @param discountType     The type of discount the coupon applies. Valid values are ("free_gift", "free gift")
     * @param couponTheme      The theme or category this coupon belongs to. Valid values are "commercial"
     * @param quantity         Indicates the quantity of the products.
     * @param activationStatus Indicates the coupon's activation status. Valid values are (active, expired).
     * @param isActive         A boolean indicating if the coupon is currently active. Valid values are (true, false)
     * @param product          indicates the product that is on discount or gift in a coupon
     * @param orderType        Specifies the type of orders the coupon applies to. Valid values are (tomorrow, later, now, both)
     * @return Coupon A Coupon object representing the newly created coupon with its properties set as per the arguments.
     */
    public Coupon createDiscountProductOrFreeGiftCoupon(User adminUser,
                                                        String couponCode,
                                                        String discountType,
                                                        String couponTheme,
                                                        int quantity,
                                                        String activationStatus,
                                                        boolean isActive,
                                                        Product product,
                                                        String orderType) {
        logger.info("Starting to create a coupon with the following characteristics:"
                + "\n Coupon Code: " + couponCode
                + "\n Discount Type: " + discountType
                + "\n Coupon Theme: " + couponTheme
                + "\n Quantity: " + quantity
                + "\n Activation Status: " + activationStatus
                + "\n Is Active: " + isActive
                + "\n Product: " + product
                + "\n Order Type: " + orderType);

        String requestBody = "{\n" +
                "    \"type\": \"" + getDiscountType(discountType) + "\",\n" +
                "    \"code\": \"" + couponCode + "\",\n" +
                "    \"active\":" + isActive + ",\n" +
                "    \"coupon_theme\": \"" + getCouponTheme(couponTheme) + "\",\n" +
                "    \"constrains\": {\n" +
                "        \"general\": {\n" +
                "            \"start_date\": \"" + getStartDate() + "\",\n" +
                "            \"end_date\": \"" + getEndDate(activationStatus) + "\",\n" +
                "            \"order_type\": \"" + getOrderType(orderType) + "\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"type_info\": {\n" +
                "        \"products\": [{\n" +
                "            \"qty\": " + quantity + ",\n" +
                "            \"id\": " + product.getMysqlId() + "\n" +
                "        }]\n" +
                "    }\n" +
                "}";

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getBaseURL() + addCouponEndpoint)
                .then()
                .statusCode(201)
                .extract().response();

        JSONObject couponObject = new JSONObject(response.getBody().asString());
        Coupon parsedCoupon = new CouponDataParser().parseCouponJsonObject(couponObject);

        if (couponObject.has("id")) {
            logger.info("Successfully created a coupon with code \""
                    + parsedCoupon.getCouponCode()
                    + "\" and id \"" + parsedCoupon.getId() + "\".");
        } else {
            logger.error("Coupon code didn't get created and couldn't find an id in the response below:");
            logger.error(response.getBody().asString());
        }
        return parsedCoupon;
    }

    // Create a start date that is today - 2days
    private static String getStartDate() {
        String pattern = "yyyy-MM-dd HH:mm:ss";
        LocalDateTime currentDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return currentDateTime.minusDays(2).format(formatter);
    }

    public static String getEndDate(String status) {
        String pattern = "yyyy-MM-dd HH:mm:ss";
        LocalDateTime currentDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);

        switch (status.toLowerCase()) {
            case "expired" -> {
                // Return current date
                return getStartDate();
            }
            default -> {
                // Return current date + 2 days
                LocalDateTime validDateTime = currentDateTime.plusDays(2);
                return validDateTime.format(formatter);
            }
        }
    }

    private String getCouponTheme(String theme) {
        return switch (theme.toLowerCase()) {
            case "commercial" -> "Commercial";
            default -> "Commercial";
        };
    }

    private String getOrderType(String orderType) {
        return switch (orderType.toLowerCase()) {
            case "tomorrow", "later" -> "later";
            case "now" -> "now";
            default -> "both";
        };
    }

    private String getDiscountType(String discountType) {
        return switch (discountType.toLowerCase()) {
            case "percent", "percentage", "%" -> "percent";
            case "free_delivery", "free delivery" -> "free_delivery";
            case "back_to_wallet", "back to wallet" -> "back_to_wallet";
            case "free_gift", "free gift" -> "free_gift";
            default -> "fixed_cart";
        };
    }
}
