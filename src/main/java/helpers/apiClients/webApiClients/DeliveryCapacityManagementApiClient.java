package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.AreaDataParser;
import helpers.dataParsers.TimeslotDataParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;

import models.Area;
import models.Configs;
import models.Timeslot;
import models.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static org.hamcrest.Matchers.*;

public class DeliveryCapacityManagementApiClient extends BaseHelper {

    Configs configs;
    private final String assignManagerToFpUrlPrefix = "/warehouses-service/warehouses/";
    private final String getAssignedFpsUrl = "/warehouses-service/warehouses/_filter";
    private final String mongoIdAndAssignedFpUrl = "/users/details";
    private final String timeslotCapacityUpdateURL = "/fps/delivery-model/timeslots/";
    private final String timeslotCapacityGeneratorUrl = "/fp/cron/timeslot-capacities/generate";
    private final String availableTimeslotsUrl = "/fps/{warehouseId}/availability";
    private final String getTimeslotsIdURL = "/fps/{warehouseId}";
    private final String postFpEtaEndpoint = "/fps/update-eta";
    private final String getAreasEndpoint = "/fps/{fpId}/areas";
    private final String etaManualSyncEndpoint = "/eta/manual-sync";

    private static final Logger logger = LoggerFactory.getLogger(DeliveryCapacityManagementApiClient.class);

    public DeliveryCapacityManagementApiClient(Configs configs) {
        this.configs = configs;
    }

    public void assignSupportManagerToFps(User adminUser, List<String> warehouseIds, String supportManagerMongoUserId) {

        for (String warehouseId : warehouseIds) {
            String assignManagerToFpUrl = assignManagerToFpUrlPrefix + warehouseId + "/managersAndDas";
            String requestBody = "{\n" +
                    "\t\"supportManagersIds\" : \"" + supportManagerMongoUserId + "\"\n" +
                    "}";

            Response response = RestAssured.given()
                    .header("Authorization", "Bearer "
                            + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                    .contentType("application/json")
                    .body(requestBody)
                    .put(configs.getBaseURL() + assignManagerToFpUrl)
                    .then()
                    .statusCode(200)
                    .extract().response();
            if (response.body().asString().equals("true")) {
                logger.info("Support manager ID '{}' is assigned successfully to warehouse '{}'", supportManagerMongoUserId, warehouseId);
            } else {
                logger.error("Support manager ID '{}' assignment failed to warehouse '{}'", supportManagerMongoUserId, warehouseId);
            }
        }
    }

    public String getUserMongoId(User adminUser) {

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .contentType("application/json") // Specify content type as JSON
                .get(configs.getLogisticsBaseURL() + mongoIdAndAssignedFpUrl)
                .then()
                .statusCode(200)
                .extract().response();

        return response.jsonPath().getString("data._id");

    }

    public boolean assignedFpstoUser(User adminUser, List<String> warehouseIds) {
        boolean mongoIdIsSupportManager = true;
        List<List<String>> allAssignedSupportManagers = new ArrayList<>();

        for (String warehouseId : warehouseIds) {

            String requestBody = "{\"project\": [{\"key\": \"basicInfo\", \"value\": 1}, {\"key\": \"users\", \"value\": 1}], \"filter\": [{\"key\": " +
                    "\"_id\", \"value\": \"" + warehouseId + "\"}]}";

            Response response = RestAssured.given()
                    .header("Authorization", "Bearer "
                            + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                    .contentType("application/json")
                    .body(requestBody)
                    .post(configs.getBaseURL() + getAssignedFpsUrl)
                    .then()
                    .statusCode(200)
                    .extract().response();
            allAssignedSupportManagers.addAll(response.jsonPath().getList("supportManagersIds"));

            for (List<String> innerList : allAssignedSupportManagers) {
                if (!innerList.contains(getUserMongoId(adminUser))) {
                    mongoIdIsSupportManager = false;
                    break;
                }
            }
        }
        return mongoIdIsSupportManager;
    }

    public List<String> getFpNamesAssigned(User adminUser, List<String> warehouseIds) {
        List<String> fpNames = new ArrayList<>();

        for (String warehouseId : warehouseIds) {
            String requestBody = "{\"project\": [{\"key\": \"basicInfo\", \"value\": 1}, {\"key\": \"users\", \"value\": 1}], \"filter\": [{\"key\": " +
                    "\"_id\", \"value\": \"" + warehouseId + "\"}]}";

            Response response = RestAssured.given()
                    .header("Authorization", "Bearer "
                            + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                    .contentType("application/json")
                    .body(requestBody)
                    .post(configs.getBaseURL() + getAssignedFpsUrl)
                    .then()
                    .statusCode(200)
                    .extract().response();
            fpNames.addAll(response.jsonPath().getList("name"));

            if (response.jsonPath().getList("name") != null) {
                logger.info("Fetched FP names for warehouseId {}: {}", warehouseIds, fpNames);
            } else {
                logger.error("Response for warehouseId {} returned null names.", warehouseIds);
            }
        }
        return fpNames;
    }

    public List<Timeslot> getAllSlotsData(User adminUser, String WarehouseId) {
        logger.info("Fetching all timeslots data for warehouseId: {}...", WarehouseId);

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .contentType("application/json") // Specify content type as JSON
                .header("token", "123456789")
                .pathParam("warehouseId", WarehouseId)
                .post(configs.getLogisticsBaseURL() + availableTimeslotsUrl)
                .then()
                .extract().response();
        if (response.getStatusCode() != 201) {
            logger.error("Response code for fetching timeslots data for warehouseId: {} is not 200. Received {}",
                    WarehouseId, response.getStatusCode());
            return new ArrayList<>();
        } else {
            JSONObject retrievedList = new JSONObject(response.getBody().asString()).optJSONObject("data");

            logger.info("Finalized fetching all timeslots data for warehouseId: {} using the availability", WarehouseId);
            logger.info("The value of instant Order availability is: {}", retrievedList.optBoolean("instant"));
            logger.info("Count of retrieved timeslots is: {}"
                    , retrievedList.optJSONArray("slots") != null
                            ? retrievedList.optJSONArray("slots").length()
                            : 0);

            return new TimeslotDataParser().parseTimeslotJsonObject(retrievedList);
        }
    }

    public void updateTimeslotCapacity(User adminUser, List<Integer> timeslotIds, int capacity) {
        for (Integer timeslotId : timeslotIds) {
            logger.info("Updating capacity for timeslot ID: {} to {}...", timeslotId, capacity);
            JSONObject requestBodyJson = new JSONObject();
            requestBodyJson.put("capacity", capacity);
            Response response = RestAssured.given()
                    .header("Authorization", "Bearer "
                            + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                    .contentType("application/json") // Specify content type as JSON
                    .body(requestBodyJson.toString())
                    .patch(configs.getLogisticsBaseURL() + timeslotCapacityUpdateURL + timeslotId)
                    .then()
                    .extract().response();

            if (response.getStatusCode() != 200) {
                logger.error("Response code for updating the capacity for timeslot: {} is not 200. Received {}",
                        timeslotId, response.getStatusCode());
            } else if (Objects.equals(response.jsonPath().get("data.capacity"), capacity)) {
                logger.info("Capacity {} updated successfully for timeslot {}", capacity, timeslotId);
            } else {
                logger.error("Failed to update capacity. Response is: {}",
                        response.getBody() != null ? response.getBody().asString() : "null");
            }
        }
    }

    public List<Integer> getAllTimeSlotsId(User adminUser, String WarehouseId) {
        logger.info("Fetching all timeslots data for warehouseId: {}", WarehouseId);

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .contentType("application/json")
                .pathParam("warehouseId", WarehouseId)
                .get(configs.getLogisticsBaseURL() + getTimeslotsIdURL)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();
        JSONObject retrievedList = new JSONObject(response.getBody().asString()).optJSONObject("data");

        logger.info("Finalized fetching all timeslots data for warehouseId: {} using the deliveryModels", WarehouseId);
        List<Integer> parsedTimeSlotIDs = new TimeslotDataParser().parseTimeslotIdsJsonObject(retrievedList);
        logger.info("Count of retrieved timeslots within the deliveryModel is: {}", parsedTimeSlotIDs.size());

        return parsedTimeSlotIDs;
    }

    public void assignDaToFp(User adminUser, User targetuser, String warehouseId) {

        String assignDaToFpUrl = "/fps/" + warehouseId + "/drivers";
        String requestBody = "{\n  \"userIds\": [" + targetuser.getId() + "]\n}";
        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getLogisticsBaseURL() + assignDaToFpUrl)
                .then()
                .statusCode(201)
                .extract().response();

        if (response.jsonPath().getString("data[0].updatedAt") != null) {
            logger.info("Driver with userId {} assigned successfully to fp {}", targetuser.getId(), warehouseId);
        } else {
            logger.error("Failed to assign driver. Response : {}", response.jsonPath().getString("data"));
        }
    }

    public void unAssignDaToFp(User adminUser, User targetuser, String warehouseId) {

        String unAssignDaToFpUrl = "/fps/" + warehouseId + "/drivers/" + targetuser.getId() + "/unassign";
        String requestBody = "{\"revokeAccess\": false}";
        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getLogisticsBaseURL() + unAssignDaToFpUrl)
                .then()
                .statusCode(201)
                .extract().response();

        if (response.jsonPath().getBoolean("data")) {
            logger.info("Driver with userId {} unassigned successfully to fp {}", targetuser.getId(), warehouseId);
        } else {
            logger.error("Failed to unassign driver. Response : {}", response.jsonPath().getString("data"));
        }
    }

    public void warmUpCapacityGenerationOnEnvironment() {
        logger.info("Triggering the fill capacity cron job...");

        Response response = RestAssured.given()
                .header("token", "123456789")
                .contentType("application/json")
                .body("{ \"instant\": true }")
                .post(configs.getLogisticsBaseURL() + timeslotCapacityGeneratorUrl)
                .then()
                .extract().response();

        response.then()
                .log().ifValidationFails()
                .statusCode(201)
                .body("success", equalTo(true))
                .body("message", equalTo("Success"));

        logger.info("Completed the capacity generator cron job call successfully.");
    }

    public Response postFpEta(User adminUser, String warehouseId) {
        logger.info("updating default ETA using warehouseId: {}", warehouseId);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("fpId", warehouseId);
        jsonObject.put("eta", 60);

        JSONArray requestBodyJson = new JSONArray();
        requestBodyJson.put(jsonObject);

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .header("token", configs.getSupplyChainStaticAuthToken())
                .contentType("application/json; charset=utf-8\n")
                .body(requestBodyJson.toString())
                .post(configs.getLogisticsBaseURL() + postFpEtaEndpoint)
                .then()
                .extract().response();
        return response;
    }

    public List<Area> getAreas(User adminUser, String warehouseId, String timeSlotId) {
        logger.info("Fetching sub areas for fpId: {}", warehouseId);

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .contentType("application/json")
                .pathParam("fpId", warehouseId)
                .queryParam("timeSlotId", timeSlotId)
                .get(configs.getLogisticsBaseURL() + getAreasEndpoint)
                .then()
                .extract().response();

        JSONObject retrievedList = new JSONObject(response.getBody().asString());
        List<Area> allSubAreas = new ArrayList<>();
        if (retrievedList.has("data")) {
            JSONArray areasArray = retrievedList.getJSONArray("data");
            if (!areasArray.isEmpty()) {
                for (int i = 0; i < areasArray.length(); i++) {
                    JSONObject subAreaJsonObject = areasArray.getJSONObject(i);
                    Area subArea = new AreaDataParser().parseSubAreaJsonObject(subAreaJsonObject);
                    allSubAreas.add(subArea);
                }
                logger.info("Successfully parsed {} sub-areas for fpId: {}", allSubAreas.size(), warehouseId);
            } else {
                logger.warn("No areas found for fpId: {}", warehouseId);
            }
        } else {
            logger.error("Response does not contain 'data' field for fpId: {}", warehouseId);
        }
        return allSubAreas;
    }

    public Response postAvailabilityEndpoint(User adminUser, String warehouseId, String timeSlotId) {
        logger.info("Fetching response for warehouseId: {} using endpoint: {}", warehouseId, availableTimeslotsUrl);

        List<Area> subAreas = getAreas(adminUser, warehouseId, timeSlotId);
        if (subAreas.isEmpty()) {
            logger.error("No areas found for warehouseId: {}", warehouseId);
            return null;
        }
        Area firstArea = subAreas.getFirst();

        JSONObject bodyJsonObject = new JSONObject();
        bodyJsonObject.put("subareaId", firstArea.getValue());

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .contentType("application/json")
                .header("token", configs.getSupplyChainStaticAuthToken())
                .body(bodyJsonObject.toString())
                .pathParam("warehouseId", warehouseId)
                .post(configs.getLogisticsBaseURL() + availableTimeslotsUrl)
                .then()
                .extract().response();
        return response;
    }

    public List<Timeslot> getAllAvailableTimeSlots(User adminUser, String warehouseId, String timeSlotId) {
        logger.info("Fetching all available timeslots including instant slot for warehouseId: {} using endpoint: {}", warehouseId, availableTimeslotsUrl);

        Response response = postAvailabilityEndpoint(adminUser, warehouseId, timeSlotId);

        JSONObject retrievedList = new JSONObject(response.getBody().asString()).optJSONObject("data");
        List<Timeslot> allTimeSlots = new ArrayList<>();
        if (!retrievedList.isEmpty() && retrievedList.has("slots")) {
            JSONArray allAvailableSlots = retrievedList.optJSONArray("slots");
            for (int i = 0; i < allAvailableSlots.length(); i++) {
                JSONObject timeslotObject = allAvailableSlots.getJSONObject(i);
                Timeslot timeslot = new Timeslot();
                timeslot.setTimeslot(timeslotObject.optString("timeslot"));
                allTimeSlots.add(timeslot);
                logger.info("retrieved available timeslot: {}", timeslot.getTimeslot());
            }
        }
        if (!retrievedList.isEmpty() && retrievedList.has("timeslot")) {
            JSONObject instantTimeslotObject = retrievedList.optJSONObject("timeslot");
            if (instantTimeslotObject != null && !instantTimeslotObject.isEmpty()) {
                logger.info("retrieved instant timeslot: {}", instantTimeslotObject.optString("timeslot"));
                Timeslot timeslot = new Timeslot();
                timeslot.setTimeslot(instantTimeslotObject.optString("timeslot"));
                allTimeSlots.add(timeslot);
            } else {
                logger.error("No instant slots available for warehouseId: {}", warehouseId);
                return null;
            }
        }
        return allTimeSlots;
    }

    public Response postEtaManualSync(User adminUser, String warehouseId, String timeSlotId) {
        logger.info("Triggering ETA manual sync  for warehouse {} and timeSlotId {} using endpoint: {}",
                warehouseId, timeSlotId, etaManualSyncEndpoint);

        List<Area> subAreas = getAreas(adminUser, warehouseId, timeSlotId);
        if (subAreas.isEmpty()) {
            logger.error("No areas found for warehouseId: {}", warehouseId);
            return null;
        }
        Area firstArea = subAreas.getFirst();

        List<Timeslot> timeslots = getAllAvailableTimeSlots(adminUser, warehouseId, timeSlotId);
        if (timeslots.isEmpty()) {
            logger.error("No timeslots found for warehouseId: {}", warehouseId);
            return null;
        }

        List<JSONObject> timeSlotsObjects = new ArrayList<>();

        for (int i = 0; i < timeslots.size(); i++) {
            Timeslot timeslot = timeslots.get(i);
            logger.info("retrieved timeslot: {}", timeslot.getTimeslot());
            JSONObject timeslotObject = new JSONObject();
            timeslotObject.put("estimated_delivery_minutes", 40);
            timeslotObject.put("timeslot", timeslot.getTimeslot());
            timeSlotsObjects.add(timeslotObject);
        }

        JSONObject bodyJsonObject = new JSONObject();
        bodyJsonObject.put("fp_id", warehouseId);
        bodyJsonObject.put("subarea_id", firstArea.getValue());
        bodyJsonObject.put("timeslots", timeSlotsObjects);

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .contentType("application/json")
                .header("token", configs.getSupplyChainStaticAuthToken())
                .body(bodyJsonObject.toString())
                .post(configs.getLogisticsBaseURL() + etaManualSyncEndpoint)
                .then()
                .extract().response();
        return response;
    }
}
