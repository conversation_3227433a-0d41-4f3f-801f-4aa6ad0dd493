package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.BatchParser;
import helpers.dataParsers.ProductsParser;
import helpers.dataParsers.StockBucketsParser;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import models.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class InventoryApiClient extends BaseHelper {
    private final Configs configs;

    private final String getProductsEndpoint = "/warehouses-service/v2/warehouses/{warehouseId}/products";
    private final String getProductBatchesEndpoint = "/warehouses-service/warehouses/{warehouseId}/batches";
    private final String getDeductEndpoint = "/warehouses-service/warehouses/{warehouseId}/batches/{action}";
    private final String listProductsEndpoint = "/supply-chain/v2/inventory/stock-management/{warehouseId}/products";
    private final String manualAdjustmentEndpoint = "/supply-chain/manual-adjustment";

    private static final Logger logger = LoggerFactory.getLogger(InventoryApiClient.class);

    public InventoryApiClient(Configs configs) {
        this.configs = configs;
    }

    public JSONObject getProductStockUpdatesResponse(User adminUser, String warehouse, int productId) {

        logger.info("Fetching product stock JSON for warehouseId: {}, productId: {}, from endpoint: {}", warehouse, productId, listProductsEndpoint);
        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .queryParam("page", "1")
                .queryParam("perPage", "30")
                .pathParam("warehouseId", warehouse)
                .queryParam("productSearch", productId)
                .get(configs.getBaseURL() + listProductsEndpoint)
                .then()
                .extract().response();

        JSONArray payloadArray = new JSONObject(response.asString()).optJSONArray("payload");

        if (payloadArray == null || payloadArray.isEmpty()) {
            logger.info("No 'payload' array found or it is empty for warehouseId: {}, productId: {}", warehouse, productId);
            return null;
        }

        for (int i = 0; i < payloadArray.length(); i++) {
            JSONObject product = payloadArray.getJSONObject(i);
            if (product.optInt("id") == productId) {
                logger.info("Product found in payload for productId: {}", productId);
                return product;
            }
        }

        logger.error("Product with ID {} not found in stock list for warehouse {}", productId, warehouse);
        return null;
    }

    public StockBuckets getParsedStockBuckets(User adminUser, String warehouseId, int productId) {

        JSONObject productJson = getProductStockUpdatesResponse(adminUser, warehouseId, productId);

        if (productJson == null || !productJson.has("stock")) {
            logger.info("Stock data not available for productId: {} in warehouseId: {}", productId, warehouseId);
            return null;
        }

        StockBuckets stockBuckets = new StockBucketsParser().parseStock(productJson.getJSONObject("stock"));

        logger.info("Successfully parsed stock buckets for productId: {} in warehouseId: {}", productId, warehouseId);
        return stockBuckets;
    }

    public List<Product> getAllProducts(User adminUser, String warehouse) {

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .queryParam("page", "1")
                .queryParam("perPage", "30")
                .pathParam("warehouseId", warehouse)
                .get(configs.getBaseURL() + getProductsEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        List<Product> allProducts = new ArrayList<>();
        List<JSONObject> productJsonObject;
        JSONArray retrievedList = new JSONObject(response.getBody().asString()).optJSONObject("payload").optJSONArray("products");

        if (!retrievedList.isEmpty()) {
            productJsonObject = new ProductsParser().parseJsonArrayToListOfJsonObjects(retrievedList);

            for (JSONObject e : productJsonObject) {
                allProducts.add(new ProductsParser().parseProductJsonObject(e));
            }
            logger.info("Products processing completed and count of products are: " + allProducts.size());
        } else {
            logger.error("Found 0 products.");
        }
        return allProducts;
    }

    public List<Batch> getProductBatches(User adminUser, String warehouse, Product product, String action) {

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .queryParam("productId", product.getMysqlId())
                .queryParam("action", getActionType(action))
                .pathParam("warehouseId", warehouse)
                .get(configs.getBaseURL() + getProductBatchesEndpoint)
                .then()
                .statusCode(200)
                .extract().response();
        logger.info("Called  GET all product batches endpoint and response code is: " + response.statusCode());
        List<Batch> allBatches = new ArrayList<>();
        List<JSONObject> batchJsonObject;
        JSONArray retrievedList = new JSONObject(response.getBody().asString()).optJSONArray("batches");

        if (!retrievedList.isEmpty()) {
            batchJsonObject = new BatchParser().parseJsonArrayToListOfJsonObjects(retrievedList);

            for (JSONObject e : batchJsonObject) {
                allBatches.add(new BatchParser().parseProductBatchJsonObject(e));
            }
            logger.info("Product batches processing completed and count of batches are: " + allBatches.size());
        } else {
            logger.error("Found 0 batches.");
        }
        return allBatches;
    }

    public List<String> reasonsList() {

        List<String> reasons = new ArrayList<>();
        reasons.add("Out of validity/Expired");
        reasons.add("Damaged");
        reasons.add("Quality");
        reasons.add("Out of validity/Expired");
        reasons.add("Returned To MainWarehouse/Supplier");
        reasons.add("Transfer Out To FP");
        reasons.add("Customer Compensation");
        reasons.add("Commercial Request: Promoted Item");
        reasons.add("Missing/Theft");
        reasons.add("Returned to MainWarehouse/Supplier");
        reasons.add("Incorrect Addition");
        reasons.add("Product Switch");
        reasons.add("Sales");

        return reasons;
}

    private String getActionType(String action) {
        return switch (action.toLowerCase()) {
            case "deduction" -> "deduct";
            case "addition" -> "add";
            default -> "deduct";
        };
    }

    public JSONArray getAllManualAdjustments(User adminUser, String warehouseId, int productId) {

        int skip = 0;
        int limit = 10;
        JSONArray allAdjustments = new JSONArray();

        while (true) {
            logger.info("Fetching the adjustment approvals with skip: {}, and limit: {}", skip, limit);
            Response response = RestAssured.given()
                    .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                    .queryParam("fpIds[]", warehouseId)
                    .queryParam("productSearch", productId)
                    .queryParam("skip", skip)
                    .queryParam("limit", limit)
                    .get(configs.getBaseURL() + manualAdjustmentEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response();

            JSONObject payload = new JSONObject(response.asString()).optJSONObject("payload");
            if (payload == null || !payload.has("adjustments") || payload.getJSONArray("adjustments").isEmpty()) {
                if (allAdjustments.isEmpty()){
                    logger.info("No adjustments payload returned for product ID: {}", productId);
                }
                else{
                    logger.info("All adjustments already returned for this product: {}", productId);
                }
                break;
            }

            JSONArray pageAdjustments = payload.getJSONArray("adjustments");
            for (int i = 0; i < pageAdjustments.length(); i++) {
                allAdjustments.put(pageAdjustments.get(i));
            }

            // Prepare for next page
            skip += limit;
        }

        logger.info("Total manual adjustments fetched for product ID {}: {}", productId, allAdjustments.length());
        return allAdjustments;
    }

    public Map<String, Object> manualStockMovements(User adminUser, String warehouseId, Product product, String reason, String action) {

        logger.info("Beginning of Manual stock movement using endpoint: {}", getDeductEndpoint);

        if (!action.equalsIgnoreCase("deduct")) {
            logger.error("Only 'deduct' action is supported at this stage. Received action: {}", action);
            return null;
        }

        List<Batch> allBatches = getProductBatches(adminUser, warehouseId, product, action);
        if (allBatches == null || allBatches.isEmpty()) {
            logger.error("No batches found for product ID: {}", product.getMysqlId());
            return null;
        }

        for (Batch batch : allBatches) {
            if (batch.getAvailable() <= 0) continue;
            logger.info("Batch with ID: {} is ignored due to available value equals to: {}", batch, batch.getAvailable());

            logger.info("Trying batch ID: {} (available: {}) for productId: {}", batch.getId(), batch.getAvailable(), product.getMysqlId());

            JSONObject requestBody = new JSONObject()
                    .put("products", new JSONArray()
                            .put(new JSONObject()
                                    .put("productId", String.valueOf(product.getMysqlId()))
                                    .put("phpId", product.getMysqlId())
                                    .put("delta", -batch.getAvailable())
                                    .put("reason", reason)
                                    .put("reasonDetail", new JSONObject().put("isTheftOrMissing", true))
                                    .put("batchId", batch.getId())
                            )
                    );

            logger.info("Sending manual stock movement request...");

            Response response = RestAssured.given()
                    .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                    .header("Content-Type", "application/json")
                    .pathParam("warehouseId", warehouseId)
                    .pathParam("action", getActionType(action))
                    .body(requestBody.toString())
                    .post(configs.getBaseURL() + getDeductEndpoint)
                    .then()
                    .extract().response();

            int statusCode = response.getStatusCode();
            String responseBody = response.asString();

            logger.info("API Response Status Code: {}", statusCode);
            logger.info("API Response Body: {}", responseBody);

            if (statusCode == 200) {
                logger.info("Manual stock movement succeeded with batchId: {}", batch.getId());
                Map<String, Object> result = new HashMap<>();
                result.put("batch", batch);
                result.put("delta", -batch.getAvailable());
                result.put("response", response); // optional — keep if needed
                return result;
            }

            if (statusCode == 500 && responseBody.contains("don't have the provided batchId")) {
                logger.info("Batch ID {} not valid for deduction, trying next available batch...", batch.getId());
                continue;
            }

            logger.error("Failed deduction for product ID {} with status {}: {}", product.getMysqlId(), statusCode, responseBody);
            return null;
        }

        logger.error("All usable batches failed. No valid batchId found for product ID: {}", product.getMysqlId());
        return null;
    }

    public Map<String, Object> deductStockAndTriggerApproval(User adminUser, String warehouseId, String reason) {

        Map<String, Object> result = new HashMap<>();

        Product product = getAllProducts(adminUser, warehouseId).getLast();
        int productId = product.getMysqlId();

        StockBuckets before = getParsedStockBuckets(adminUser, warehouseId, productId);

        // Retry logic now happens internally here
        Map<String, Object> movementResult = manualStockMovements(adminUser, warehouseId, product, reason, "deduct");
        if (movementResult == null) {
            logger.error("Manual stock movement failed for product ID: {}", productId);
            return null;
        }

        Batch batch = (Batch) movementResult.get("batch");
        Response response = (Response) movementResult.get("response");

        StockBuckets after = getParsedStockBuckets(adminUser, warehouseId, productId);

        result.put("response", response);
        result.put("product", product);
        result.put("batch", batch);
        result.put("delta", movementResult.get("delta"));
        result.put("stockBefore", before);
        result.put("stockAfter", after);

        return result;
    }

    public Response deductStock(User adminUser, String warehouse, Product product, Batch batch, String reason, String action) {
        String requestBody = "{\n" +
                "  \"products\":\n" +
                "  [\n" +
                "    {\n" +
                "      \"productId\": \"" + product.getMysqlId() + "\", \n" +
                "      \"phpId\": " + product.getMysqlId() + ", \n" +
                "      \"delta\": -" + product.getNowStock() + ",\n" +
                "      \"reason\": \"" + reason + "\",\n" +
                "      \"reasonDetail\": {\n" +
                "        \"isTheftOrMissing\": true\n" +
                "      },\n" +
                "      \"batchId\": " + batch.getId() + " \n" +
                "    } \n" +
                "  ]\n" +
                "}";

        // Log the product information and request body
        logger.info("Initiating deduct stock API call");
        logger.info("Request Body: " + requestBody);
        logger.info("Product ID: " + product.getMysqlId());

        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .header("content-type", "application/json")
                .pathParam("warehouseId", warehouse)
                .body(requestBody)
                .pathParam("action", getActionType(action)) // Ensure correct action type (deduct or add)
                .post(configs.getBaseURL() + getDeductEndpoint)
                .then()
                .extract().response();

        // Log the response details
        logger.info("API Response Status Code: " + response.getStatusCode());
        logger.info("API Response Body: " + response.asString());

        return response;

    }

    public boolean hasMatchingAdjustment(JSONArray adjustments, int expectedBatchId, int expectedDelta, String expectedReason) {

        logger.info("Verifying existence of manual adjustment for Batch: {} in the adjustments array with reason: {} and delta: {}",
                expectedBatchId, expectedReason, expectedDelta);
        if (adjustments == null) return false;

        for (int i = 0; i < adjustments.length(); i++) {
            JSONObject adj = adjustments.getJSONObject(i);
            if (!expectedReason.equals(adj.optString("reason"))) continue;

            JSONArray products = adj.optJSONArray("products");
            if (products == null) continue;

            for (int j = 0; j < products.length(); j++) {
                JSONObject p = products.getJSONObject(j);
                JSONObject b = p.optJSONObject("batch");

                if (b != null &&
                        b.optInt("id") == expectedBatchId &&
                        p.optInt("adjustmentQuantity") == expectedDelta) {
                    logger.info("Manual adjustment was found successfully for Batch: {} in the adjustments array with reason: {} and delta: {}",
                            expectedBatchId, expectedReason, expectedDelta);
                    return true;
                }
            }
        }
        return false;
    }
}
