package helpers.dataParsers;

import models.StockBuckets;
import org.json.JSONObject;

public class StockBucketsParser {

    public StockBuckets parseStock(JSONObject stockObject) {
        StockBuckets stock = new StockBuckets();

        if (stockObject == null) {
            throw new IllegalArgumentException("Stock object is null and cannot be parsed.");
        }

        stock.setOnApp(stockObject.optDouble("onApp"));
        stock.setReserved(stockObject.optDouble("reserved"));
        stock.setFpStock(stockObject.optDouble("fpStock"));
        stock.setNotSellable(stockObject.optDouble("notSellable"));
        stock.setMissing(stockObject.optDouble("missing"));
        stock.setTotalLiability(stockObject.optDouble("totalLiability"));

        return stock;
    }
}
