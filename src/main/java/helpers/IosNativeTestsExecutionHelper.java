package helpers;

import io.appium.java_client.AppiumBy;
import io.appium.java_client.TouchAction;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.touch.WaitOptions;
import io.appium.java_client.touch.offset.PointOption;
import modals.customerApp.iosNative.*;
import modals.customerApp.iosNative.foodAggregator.IosNativeFoodAggregatorHomeScreen;
import modals.customerApp.iosNative.iosNativeHomePage.IosNativeHomeScreen;
import modals.customerApp.iosNative.iosNativeMorePage.IosNativeMoreScreen;
import models.BusinessCategory;
import models.Restaurant;
import models.TestData;
import models.ValidationResults;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.Rectangle;
import org.openqa.selenium.WebElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

public class IosNativeTestsExecutionHelper extends IosTestsExecutionHelper {
    @FunctionalInterface
    public interface RestaurantVerifier {
        boolean verify(String restaurantId);
    }

    private static final Logger logger = LoggerFactory.getLogger(IosTestsExecutionHelper.class);

    public void register(IosNativeCountriesSelectionScreen iosNativeCountriesSelectionScreen,
                         IosNativeLandingScreen iosNativeLandingScreen,
                         IosNativePhoneNumberScreen iosNativePhoneNumberScreen,
                         IosNativePhoneCountrySelectionDropdownScreen iosNativePhoneCountrySelectionDropdownScreen,
                         IosNativeOtpVerificationScreen iosNativeOtpVerificationScreen,
                         TestExecutionHelper testExecutionHelper,
                         TestData testData,
                         IosNativeCreateAccountScreen iosNativeCreateAccountScreen,
                         IosNativeRegisterSuccessScreen iosNativeRegisterSuccessScreen,
                         IosNativeHomeScreen iosNativeHomeScreen,
                         String phoneCountry) {

        // Choose a country and press login
        iosNativeCountriesSelectionScreen.selectCountryAndProceed(testData.getTestCountryCode());

        //Press the Login or signup button
        iosNativeLandingScreen.pressAuthBtn();

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosNativePhoneNumberScreen
                        , iosNativeOtpVerificationScreen, "register", phoneCountry);
            }
            default -> {
                changeCountry(iosNativePhoneNumberScreen, iosNativePhoneCountrySelectionDropdownScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosNativePhoneNumberScreen
                        , iosNativeOtpVerificationScreen, "register", phoneCountry);
            }
        }

        //Enter Account Information
        iosNativeCreateAccountScreen.fillInAccountInformationForm(testData);
        iosNativeCreateAccountScreen.pressSubmitBtn();

        //Proceed from the success screen
        if (iosNativeRegisterSuccessScreen.isPageDisplayed())
            iosNativeRegisterSuccessScreen.pressDoneBtn();

        //Validate that home screen is displayed
        iosNativeHomeScreen.isPageDisplayed();
    }

    public void login(TestData testData, TestExecutionHelper testExecutionHelper
            , IosNativePhoneNumberScreen iosNativePhoneNumberScreen
            , IosNativePhoneCountrySelectionDropdownScreen iosNativePhoneCountrySelectionDropdownScreen
            , IosNativeOtpVerificationScreen iosNativeOtpVerificationScreen
            , String phoneCountry) {

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosNativePhoneNumberScreen, iosNativeOtpVerificationScreen,
                        "login", phoneCountry);
            }
            default -> {
                changeCountry(iosNativePhoneNumberScreen, iosNativePhoneCountrySelectionDropdownScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosNativePhoneNumberScreen, iosNativeOtpVerificationScreen,
                        "login", phoneCountry);
            }
        }
    }

    private void enterPhoneNumberAndOTP(TestData testData, TestExecutionHelper testExecutionHelper,
                                        IosNativePhoneNumberScreen iosNativePhoneNumberScreen,
                                        IosNativeOtpVerificationScreen iosNativeOtpVerificationScreen,
                                        String method, String phoneCountry) {

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                if (iosNativePhoneNumberScreen.isPageDisplayed())
                    iosNativePhoneNumberScreen.enterPhoneNumberAndPresNext(
                            testData.getRandomTestUser().getLocalPhoneNumber());
            }
            default -> {
                if (iosNativePhoneNumberScreen.isPageDisplayed())
                    iosNativePhoneNumberScreen.enterPhoneNumberAndPresNext(
                            testData.getRandomTestUser().getForeignLocalPhoneNumber());
            }
        }

        //Fetch and Enter the OTP
        if (iosNativeOtpVerificationScreen.isPageDisplayed()) {
            while (iosNativeOtpVerificationScreen.isPageDisplayed()) {
                try {
                    testData.setRandomTestUser(testExecutionHelper.otpFactory
                            .fetchOtp(testData, method, testData.getRandomTestUser()));
                    if (iosNativeOtpVerificationScreen.isPageDisplayed()) {
                        iosNativeOtpVerificationScreen.enterOtp(testData.getRandomTestUser().getOtp());
                    }
                } catch (Exception e) {
                    break;
                }
                if (iosNativeOtpVerificationScreen.isPageHidden())
                    break;
            }
        }
    }

    public void logoutAndGoToPhoneInputScreen(IOSDriver iosDriver, IosNativeHomeScreen iosNativeHomeScreen
            , IosNativeMoreScreen iosNativeMoreScreen) {
        iosNativeHomeScreen.pressMoreBtn();

        scrollUntilACertainElementIsFound(iosDriver,
                "down",
                iosNativeMoreScreen.getScrollableContentContainer(),
                iosNativeMoreScreen.getLogoutNameSelector());

        iosNativeMoreScreen.pressLogoutBtn();

        iosNativeHomeScreen.isPageDisplayed();

        iosNativeHomeScreen.pressMoreBtn();
        iosNativeMoreScreen.pressContinueBtn();
    }

    private void changeCountry(IosNativePhoneNumberScreen iosNativePhoneNumberScreen
            , IosNativePhoneCountrySelectionDropdownScreen iosNativePhoneCountrySelectionDropdownScreen,
                               TestData testData) {
        iosNativePhoneNumberScreen.pressPhoneNumberCountryCode();
        if (iosNativePhoneCountrySelectionDropdownScreen.isDropdownDisplayed()) {
            iosNativePhoneCountrySelectionDropdownScreen.searchAndSelectTheCountry(testData.getRandomTestUser().getForeignPhoneCountry()
                    , testData.getRandomTestUser().getForeignPhoneNumber()
                            .replace(testData.getRandomTestUser().getForeignLocalPhoneNumber(), ""));
        }
    }

    public ValidationResults areExpectedRestaurantsDisplayed(IOSDriver iosDriver,
                                                             IosNativeFoodAggregatorHomeScreen iosNativeFoodAggregatorHomeScreen,
                                                             List<Restaurant> expectedRestaurants,
                                                             ValidationResults validationResults) {

        // Track all restaurants discovered during targeted scrolling
        Set<String> discoveredRestaurantIds = new HashSet<>();

        // Create set of expected restaurant IDs for quick lookup
        Set<String> expectedRestaurantIds = expectedRestaurants.stream()
                .map(restaurant -> String.valueOf(restaurant.getYeloId()))
                .collect(Collectors.toSet());

        try {
            for (Restaurant restaurant : expectedRestaurants) {

                String restaurantId = String.valueOf(restaurant.getYeloId());

                // Capture visible restaurants before scrolling
                iosNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleRestaurants(
                        discoveredRestaurantIds);

                // Check if element is already visible before scrolling
                if (!iosNativeFoodAggregatorHomeScreen.isRestaurantCardDisplayed(restaurantId)
                        || isElementPartiallyDisplayed(iosDriver
                        , iosNativeFoodAggregatorHomeScreen.getRestaurantCardUiElement(restaurantId), "down", 0.05)
                        || !iosNativeFoodAggregatorHomeScreen.isRestaurantNameDisplayed(restaurantId)) {

                    //Scroll until the restaurant name is visible
                    scrollUntilACertainElementIsFound(iosDriver, "down",
                            iosNativeFoodAggregatorHomeScreen.getRestaurantsScrollableContainer(),
                            iosNativeFoodAggregatorHomeScreen.getRestaurantNameIdSelector(restaurantId));

                    // Capture visible restaurants after scrolling
                    iosNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleRestaurants(
                            discoveredRestaurantIds);
                }

                if (iosNativeFoodAggregatorHomeScreen.isRestaurantNameDisplayed(restaurantId)) {
                    String displayedName = iosNativeFoodAggregatorHomeScreen.getRestaurantName(restaurantId);

                    if (!displayedName.equalsIgnoreCase(restaurant.getName())) {
                        validationResults.setResult(false);
                        validationResults.addALogToValidationResults("Restaurant with ID: " + restaurant.getYeloId()
                                + " has incorrect name displayed. Expected: \"" + restaurant.getName()
                                + "\", Actual: \"" + displayedName + "\"\n");
                    }

                } else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("Restaurant with ID: " + restaurant.getYeloId()
                            + " and name \"" + restaurant.getName() + "\" is not displayed\n");
                }
            }

            // Check for unexpected restaurants that were discovered during scrolling
            Set<String> unexpectedIds = new HashSet<>(discoveredRestaurantIds);
            unexpectedIds.removeAll(expectedRestaurantIds);

            if (!unexpectedIds.isEmpty()) {
                validationResults.setResult(false);
                for (String unexpectedId : unexpectedIds) {
                    String unexpectedName = "Unknown";
                    try {
                        unexpectedName = iosNativeFoodAggregatorHomeScreen.getRestaurantName(unexpectedId);
                    } catch (Exception e) {
                        // Keep "Unknown" if name retrieval fails
                    }

                    validationResults.addALogToValidationResults(
                            "UNEXPECTED restaurant found - ID: " + unexpectedId +
                                    ", Name: \"" + unexpectedName + "\"\n");
                }
                validationResults.addALogToValidationResults(
                        "Total unexpected restaurants found: " + unexpectedIds.size() + "\n");
            }

            if (!expectedRestaurants.isEmpty()) {
                // Final capture at the top
                scrollUntilACertainElementIsFound(iosDriver, "up",
                        iosNativeFoodAggregatorHomeScreen.getRestaurantsScrollableContainer(),
                        iosNativeFoodAggregatorHomeScreen.getRestaurantCardIdSelector(
                                String.valueOf(expectedRestaurants.getFirst().getYeloId())));

                iosNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleRestaurants(discoveredRestaurantIds);
            }

        } catch (Exception e) {
            validationResults.setResult(false);
            validationResults.addALogToValidationResults("Error during restaurant validation: " + e.getMessage() + "\n");
        }

        return validationResults;
    }

    public ValidationResults areExpectedCategoryFiltersDisplayed(IOSDriver iosDriver,
                                                                 IosNativeFoodAggregatorHomeScreen iosNativeFoodAggregatorHomeScreen,
                                                                 List<BusinessCategory> expectedCategories,
                                                                 ValidationResults validationResults) {

        // Track all categories discovered during targeted scrolling
        Set<String> discoveredCategoryIds = new HashSet<>();

        // Create set of expected category IDs for quick lookup (no intermediate mapping needed)
        Set<String> expectedCategoryIds = expectedCategories.stream()
                .filter(category -> category.getYeloId() > 0)
                .map(category -> String.valueOf(category.getYeloId()))
                .collect(Collectors.toSet());

        try {
            // For each expected category, validate directly
            for (BusinessCategory expectedCategory : expectedCategories) {
                String categoryName = expectedCategory.getName();
                Integer categoryId = expectedCategory.getYeloId();

                // Validate category data
                if (categoryName == null || categoryId == null) {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("Invalid category data - Name: \"" + categoryName + "\", ID: " + categoryId + "\n");
                    continue;
                }

                String categoryIdStr = String.valueOf(categoryId);

                // Capture visible categories before scrolling
                iosNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleCategories(discoveredCategoryIds);

                //Check if element is already visible or partially visible
                if (!iosNativeFoodAggregatorHomeScreen.isCategoryFilterDisplayed(categoryIdStr) || isElementPartiallyDisplayed(iosDriver
                        , iosNativeFoodAggregatorHomeScreen.getCategoryFilterUiElement(categoryIdStr), "right", 0.05)
                        || !iosNativeFoodAggregatorHomeScreen.isCategoryFilterNameDisplayed(categoryIdStr)) {

                    // Targeted scroll to find the specific category by ID
                    scrollUntilACertainElementIsFound(iosDriver,
                            "right",
                            iosNativeFoodAggregatorHomeScreen.getFilterScrollableContainer(),
                            iosNativeFoodAggregatorHomeScreen.getCategoryFilterIdSelector(categoryIdStr));

                    // Capture visible categories after scrolling
                    iosNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleCategories(discoveredCategoryIds);
                }

                // Validate the target category
                if (iosNativeFoodAggregatorHomeScreen.isCategoryFilterDisplayed(categoryIdStr)) {
                    String displayedName;
                    //Check if the name is also visible if not scroll until the name is
                    if (iosNativeFoodAggregatorHomeScreen.isCategoryFilterNameDisplayed(categoryIdStr)) {
                        displayedName = iosNativeFoodAggregatorHomeScreen.extractCategoryFilterName(categoryIdStr);

                        if (!displayedName.equalsIgnoreCase(categoryName.trim())) {
                            validationResults.setResult(false);
                            validationResults.addALogToValidationResults("Category Filter with ID: " + categoryId
                                    + " has incorrect name displayed. Expected: \"" + categoryName.trim()
                                    + "\", Actual: \"" + displayedName + "\"\n");
                        }
                    } else scrollUntilACertainElementIsFound(iosDriver, "right",
                            iosNativeFoodAggregatorHomeScreen.getFilterScrollableContainer(),
                            iosNativeFoodAggregatorHomeScreen.getCategoryFilterNameIdSelector(categoryIdStr));
                } else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("Category Filter with ID: " + categoryId
                            + " and name \"" + categoryName.trim() + "\" is not displayed\n");
                }
            }

            // Check for unexpected categories that were discovered during scrolling
            Set<String> unexpectedIds = new HashSet<>(discoveredCategoryIds);
            unexpectedIds.removeAll(expectedCategoryIds);

            if (!unexpectedIds.isEmpty()) {
                validationResults.setResult(false);
                for (String unexpectedId : unexpectedIds) {
                    // Get name only when needed for reporting
                    String unexpectedName = "Unknown";
                    try {
                        unexpectedName = iosNativeFoodAggregatorHomeScreen.extractCategoryFilterName(unexpectedId);
                    } catch (Exception e) {
                        // Keep "Unknown" if name retrieval fails
                    }

                    validationResults.addALogToValidationResults(
                            "UNEXPECTED category found - ID: " + unexpectedId +
                                    ", Name: \"" + unexpectedName + "\"\n");
                }
                validationResults.addALogToValidationResults(
                        "Total unexpected categories found: " + unexpectedIds.size() + "\n");
            }

            // Scroll back to the first category (direct access, no mapping needed)
            if (!expectedCategories.isEmpty()) {
                String firstCategoryId = String.valueOf(expectedCategories.getFirst().getYeloId());
                scrollUntilACertainElementIsFound(iosDriver,
                        "left",
                        iosNativeFoodAggregatorHomeScreen.getFilterScrollableContainer(),
                        iosNativeFoodAggregatorHomeScreen.getCategoryFilterIdSelector(firstCategoryId));

                // Final capture after scrolling back
                iosNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleCategories(discoveredCategoryIds);
            }

        } catch (Exception e) {
            validationResults.setResult(false);
            validationResults.addALogToValidationResults("Error during category validation: " + e.getMessage() + "\n");
        }

        return validationResults;
    }

    public ValidationResults progressiveScrollAndVerifyAllRestaurants(
            IOSDriver driver,
            IosNativeFoodAggregatorHomeScreen screen,
            List<Restaurant> allRestaurants,
            IosNativeTestsExecutionHelper.RestaurantVerifier verifier,
            String contextLogMessage
    ) {
        ValidationResults validationResults = new ValidationResults();

        Set<String> verifiedRestaurants = new HashSet<>();
        Set<String> expectedRestaurantIds = allRestaurants.stream()
                .map(r -> String.valueOf(r.getYeloId()))
                .collect(Collectors.toSet());

        try {
            int scrollAttempts = 0;
            int maxScrollAttempts = 50;
            int extraScrolls = 3;  // Allow 3 more scrolls after no movement
            boolean canScrollMore = true;

            while ((canScrollMore || extraScrolls-- > 0) &&
                    scrollAttempts < maxScrollAttempts &&
                    verifiedRestaurants.size() < expectedRestaurantIds.size()) {

                scrollAttempts++;

                Set<String> currentlyVisible = screen.getCurrentlyVisibleRestaurants();
                logger.info("🔃 Scroll {}: Visible restaurant IDs: {}", scrollAttempts, currentlyVisible);

                List<String> visibleList = new ArrayList<>(currentlyVisible);
                Collections.sort(visibleList); // optional: keeps scroll log consistent

                for (String restaurantId : visibleList) {
                    if (expectedRestaurantIds.contains(restaurantId) && !verifiedRestaurants.contains(restaurantId)) {

                        Restaurant restaurant = allRestaurants.stream()
                                .filter(r -> String.valueOf(r.getYeloId()).equals(restaurantId))
                                .findFirst()
                                .orElse(null);

                        boolean passed = verifier.verify(restaurantId);

                        if (!passed) {
                            validationResults.setResult(false);
                            validationResults.addALogToValidationResults("❌ Verification failed for restaurant ID: " + restaurantId + "\n");
                        } else {
                            logger.info("✅ Passed verification for restaurant: {} (ID: {})",
                                    restaurant != null ? restaurant.getName() : "Unknown", restaurantId);
                        }

                        verifiedRestaurants.add(restaurantId);
                    }
                }

                Thread.sleep(800); // Allow UI to settle before scroll

                canScrollMore = performIncrementalScroll(driver, screen.getRestaurantsScrollableContainer(), "down", screen);

                Thread.sleep(900); // Allow new elements to fully load/render after scroll
            }

            // Final check: any restaurant still not verified?
            Set<String> notFound = new HashSet<>(expectedRestaurantIds);
            notFound.removeAll(verifiedRestaurants);
            for (String id : notFound) {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("⚠️ Could not find restaurant during scrolling: " + id + "\n");
            }

            logger.info("📊 {} Summary: {}/{} restaurants verified",
                    contextLogMessage, verifiedRestaurants.size(), expectedRestaurantIds.size());

        } catch (Exception e) {
            validationResults.setResult(false);
            validationResults.addALogToValidationResults("❌ Error during verification: " + e.getMessage() + "\n");
        }

        return validationResults;
    }
    /**
     * Performs a small incremental scroll for iOS
     *
     */
    private boolean performIncrementalScroll(IOSDriver driver, WebElement scrollView, String direction, IosNativeFoodAggregatorHomeScreen screen) {
        try {
            String beforeScrollState = getScrollState(screen.getRestaurantsScrollableContainer(), screen);

            JavascriptExecutor js = (JavascriptExecutor) driver;
            Map<String, Object> params = new HashMap<>();
            params.put("direction", direction);
            params.put("element", scrollView);
            params.put("toVisible", false);
            params.put("distance", 0.9);

            js.executeScript("mobile: scroll", params);

            Thread.sleep(200);

            String afterScrollState = getScrollState(screen.getRestaurantsScrollableContainer(), screen);
            return !beforeScrollState.equals(afterScrollState);

        } catch (Exception e) {
            logger.warn("Error during alternative incremental scroll: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Helper method to get current scroll state for comparison
     */
    private String getScrollState(WebElement scrollView, IosNativeFoodAggregatorHomeScreen screen) {
        try {
            Set<String> visibleRestaurants = screen.getCurrentlyVisibleRestaurants();
            if (visibleRestaurants.isEmpty()) {
                return "empty";
            }

            // Convert to sorted list to get first and last
            List<String> sortedIds = new ArrayList<>(visibleRestaurants);
            Collections.sort(sortedIds);

            String firstVisible = sortedIds.get(0);
            String lastVisible = sortedIds.get(sortedIds.size() - 1);

            return firstVisible + "_" + lastVisible + "_" + visibleRestaurants.size();
        } catch (Exception e) {
            return "unknown_" + System.currentTimeMillis();
        }
    }
}
