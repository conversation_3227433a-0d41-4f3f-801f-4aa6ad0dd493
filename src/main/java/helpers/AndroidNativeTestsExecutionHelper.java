package helpers;

import io.appium.java_client.AppiumBy;
import io.appium.java_client.android.AndroidDriver;
import modals.customerApp.androidNative.*;
import modals.customerApp.androidNative.AndroidNativeMoreScreen.AndroidNativeMoreScreen;
import modals.customerApp.androidNative.androidNativeHomePage.AndroidNativeHomeScreen;
import modals.customerApp.androidNative.foodAggregator.AndroidNativeFoodAggregatorHomeScreen;
import models.BusinessCategory;
import models.Restaurant;
import models.TestData;
import models.ValidationResults;
import org.openqa.selenium.WebElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.*;
import java.util.stream.Collectors;

public class AndroidNativeTestsExecutionHelper extends AndroidTestsExecutionHelper {
    @FunctionalInterface
    public interface RestaurantVerifier {
        boolean verify(String restaurantId);
    }

    private static final Logger logger = LoggerFactory.getLogger(AndroidTestsExecutionHelper.class);

    public void register(AndroidNativeCountriesSelectionScreen androidNativeCountriesSelectionScreen,
                         AndroidNativeLandingScreen androidNativeLandingScreen,
                         AndroidNativePhoneNumberScreen androidNativePhoneNumberScreen,
                         AndroidNativePhoneCountrySelectionDropdownScreen androidNativePhoneCountrySelectionDropdownScreen,
                         AndroidNativeOtpVerificationScreen androidNativeOtpVerificationScreen,
                         TestExecutionHelper testExecutionHelper,
                         TestData testData,
                         AndroidNativeCreateAccountScreen androidNativeCreateAccountScreen,
                         AndroidNativeRegisterSuccessScreen androidNativeRegisterSuccessScreen,
                         AndroidNativeHomeScreen androidNativeHomeScreen,
                         String phoneCountry) {

        // Choose a country and press login
        androidNativeCountriesSelectionScreen.selectCountryAndProceed(testData.getTestCountryCode());

        //Press the Login or signup button
        androidNativeLandingScreen.pressAuthBtn();

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidNativePhoneNumberScreen
                        , androidNativeOtpVerificationScreen, "register", phoneCountry);
            }
            default -> {
                changeCountry(androidNativePhoneNumberScreen, androidNativePhoneCountrySelectionDropdownScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidNativePhoneNumberScreen
                        , androidNativeOtpVerificationScreen, "register", phoneCountry);
            }
        }

        //Enter Account Information
        androidNativeCreateAccountScreen.fillInAccountInformationForm(testData);
        androidNativeCreateAccountScreen.pressSubmitBtn();

        //Proceed from the success screen
        if (androidNativeRegisterSuccessScreen.isPageDisplayed())
            androidNativeRegisterSuccessScreen.pressDoneBtn();

        //Validate that home screen is displayed
        androidNativeHomeScreen.isPageDisplayed();
    }

    public void login(TestData testData, TestExecutionHelper testExecutionHelper
            , AndroidNativePhoneNumberScreen androidNativePhoneNumberScreen
            , AndroidNativePhoneCountrySelectionDropdownScreen androidNativePhoneCountrySelectionDropdownScreen
            , AndroidNativeOtpVerificationScreen androidNativeOtpVerificationScreen
            , String phoneCountry) {

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidNativePhoneNumberScreen, androidNativeOtpVerificationScreen,
                        "login", phoneCountry);
            }
            default -> {
                changeCountry(androidNativePhoneNumberScreen, androidNativePhoneCountrySelectionDropdownScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidNativePhoneNumberScreen, androidNativeOtpVerificationScreen,
                        "login", phoneCountry);
            }
        }
    }

    private void enterPhoneNumberAndOTP(TestData testData, TestExecutionHelper testExecutionHelper,
                                        AndroidNativePhoneNumberScreen androidNativePhoneNumberScreen,
                                        AndroidNativeOtpVerificationScreen androidNativeOtpVerificationScreen,
                                        String method, String phoneCountry) {

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                if (androidNativePhoneNumberScreen.isPageDisplayed())
                    androidNativePhoneNumberScreen.enterPhoneNumberAndPresNext(
                            testData.getRandomTestUser().getLocalPhoneNumber());
            }
            default -> {
                if (androidNativePhoneNumberScreen.isPageDisplayed())
                    androidNativePhoneNumberScreen.enterPhoneNumberAndPresNext(
                            testData.getRandomTestUser().getForeignLocalPhoneNumber());
            }
        }

        //Fetch and Enter the OTP
        if (androidNativeOtpVerificationScreen.isPageDisplayed()) {
            while (androidNativeOtpVerificationScreen.isPageDisplayed()) {
                try {
                    testData.setRandomTestUser(testExecutionHelper.otpFactory
                            .fetchOtp(testData, method, testData.getRandomTestUser()));
                    if (androidNativeOtpVerificationScreen.isPageDisplayed()) {
                        androidNativeOtpVerificationScreen.enterOtp(testData.getRandomTestUser().getOtp());
                    }
                    if (androidNativeOtpVerificationScreen.isPageDismissed())
                        break;
                } catch (Exception e) {
                    break;
                }
            }
        }
    }

    public void logoutAndGoToPhoneInputScreen(AndroidDriver androidDriver, AndroidNativeHomeScreen androidNativeHomeScreen
            , AndroidNativeMoreScreen androidNativeMoreScreen) {
        androidNativeHomeScreen.pressMoreBtn();

        scrollUntilACertainElementIsFound(androidDriver,
                androidNativeMoreScreen.getScrollableContentContainer(),
                "down",
                androidNativeMoreScreen.getLogoutBtnContentDescription());

        androidNativeMoreScreen.pressLogoutBtn();

        androidNativeHomeScreen.isPageDisplayed();

        androidNativeHomeScreen.pressMoreBtn();
        androidNativeMoreScreen.pressContinueBtn();
    }

    private void changeCountry(AndroidNativePhoneNumberScreen androidNativePhoneNumberScreen
            , AndroidNativePhoneCountrySelectionDropdownScreen androidNativePhoneCountrySelectionDropdownScreen,
                               TestData testData) {
        androidNativePhoneNumberScreen.pressPhoneNumberCountryCode();
        if (androidNativePhoneCountrySelectionDropdownScreen.isDropdownDisplayed()) {
            androidNativePhoneCountrySelectionDropdownScreen
                    .searchAndSelectTheCountry(testData.getRandomTestUser().getForeignPhoneCountry()
                            , testData.getRandomTestUser().getForeignPhoneNumber()
                                    .replace(testData.getRandomTestUser().getForeignLocalPhoneNumber(), ""));
        }
    }

    public ValidationResults areExpectedRestaurantsDisplayed(AndroidDriver androidDriver,
                                                             AndroidNativeFoodAggregatorHomeScreen androidNativeFoodAggregatorHomeScreen,
                                                             List<Restaurant> expectedRestaurants,
                                                             ValidationResults validationResults) {

        // Track all restaurants discovered during targeted scrolling
        Set<String> discoveredRestaurantIds = new HashSet<>();

        // Create set of expected restaurant IDs for quick lookup
        Set<String> expectedRestaurantIds = expectedRestaurants.stream()
                .map(restaurant -> String.valueOf(restaurant.getYeloId()))
                .collect(Collectors.toSet());

        try {
            for (Restaurant restaurant : expectedRestaurants) {
                String restaurantId = String.valueOf(restaurant.getYeloId());

                // Capture visible restaurants before scrolling
                androidNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleRestaurants(
                        discoveredRestaurantIds);

                // Check if element is already visible before scrolling
                if (!androidNativeFoodAggregatorHomeScreen.isRestaurantCardDisplayed(restaurantId)
                        || isElementPartiallyDisplayed(androidDriver
                        , androidNativeFoodAggregatorHomeScreen.getRestaurantCardUiElement(restaurantId),"down",0.05 )
                        || !androidNativeFoodAggregatorHomeScreen.isRestaurantNameDisplayed(restaurantId)) {

                    scrollUntilACertainElementIsFound(androidDriver,
                            androidNativeFoodAggregatorHomeScreen.getRestaurantsScrollableContainer(),
                            "down",
                            "id=" + androidNativeFoodAggregatorHomeScreen.getRestaurantCardIdSelector(restaurantId));

                    // Capture visible restaurants after scrolling
                    androidNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleRestaurants(
                            discoveredRestaurantIds);
                }

                if (androidNativeFoodAggregatorHomeScreen.isRestaurantCardDisplayed(restaurantId)) {
                    String displayedName = androidNativeFoodAggregatorHomeScreen.getRestaurantName(restaurantId);

                    if (!displayedName.equalsIgnoreCase(restaurant.getName())) {
                        validationResults.setResult(false);
                        validationResults.addALogToValidationResults("Restaurant with ID: " + restaurant.getYeloId()
                                + " has incorrect name displayed. Expected: \"" + restaurant.getName()
                                + "\", Actual: \"" + displayedName + "\"\n");
                    }
                } else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("Restaurant with ID: " + restaurant.getYeloId()
                            + " and name \"" + restaurant.getName() + "\" is not displayed\n");
                }
            }

            // Check for unexpected restaurants that were discovered during scrolling
            Set<String> unexpectedIds = new HashSet<>(discoveredRestaurantIds);
            unexpectedIds.removeAll(expectedRestaurantIds);

            if (!unexpectedIds.isEmpty()) {
                validationResults.setResult(false);
                for (String unexpectedId : unexpectedIds) {
                    String unexpectedName = "Unknown";
                    try {
                        unexpectedName = androidNativeFoodAggregatorHomeScreen.getRestaurantName(unexpectedId);
                    } catch (Exception e) {
                        // Keep "Unknown" if name retrieval fails
                    }

                    validationResults.addALogToValidationResults(
                            "UNEXPECTED restaurant found - ID: " + unexpectedId +
                                    ", Name: \"" + unexpectedName + "\"\n");
                }
                validationResults.addALogToValidationResults(
                        "Total unexpected restaurants found: " + unexpectedIds.size() + "\n");
            }

            if (!expectedRestaurants.isEmpty()) {
                // Final capture at the top
                scrollUntilACertainElementIsFound(androidDriver,
                        androidNativeFoodAggregatorHomeScreen.getRestaurantsScrollableContainer(),
                        "up",
                        "id="+androidNativeFoodAggregatorHomeScreen.getRestaurantCardIdSelector(
                                String.valueOf(expectedRestaurants.getFirst().getYeloId())));

                androidNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleRestaurants(discoveredRestaurantIds);
            }

        } catch (Exception e) {
            validationResults.setResult(false);
            validationResults.addALogToValidationResults("Error during restaurant validation: " + e.getMessage() + "\n");
        }

        return validationResults;
    }

    public ValidationResults areExpectedCategoryFiltersDisplayed(AndroidDriver androidDriver,
                                                                 AndroidNativeFoodAggregatorHomeScreen androidNativeFoodAggregatorHomeScreen,
                                                                 List<BusinessCategory> expectedCategories,
                                                                 ValidationResults validationResults) {

        // Track all categories discovered during targeted scrolling
        Set<String> discoveredCategoryIds = new HashSet<>();

        // Create set of expected category IDs for quick lookup
        Set<String> expectedCategoryIds = expectedCategories.stream()
                .filter(category -> category.getYeloId() > 0) // Use > 0 instead of != null
                .map(category -> String.valueOf(category.getYeloId()))
                .collect(Collectors.toSet());

        try {
            // For each expected category, validate directly
            for (BusinessCategory expectedCategory : expectedCategories) {
                String categoryName = expectedCategory.getName();
                int categoryId = expectedCategory.getYeloId(); // primitive int

                // Validate category data - check for invalid int values instead of null
                if (categoryName == null || categoryId <= 0) { // Assuming <= 0 means invalid
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("Invalid category data - Name: \"" + categoryName + "\", ID: " + categoryId + "\n");
                    continue;
                }

                String categoryIdStr = String.valueOf(categoryId);

                // Capture visible categories before scrolling
                androidNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleCategories(discoveredCategoryIds);

                //Check if element is already visible or partially visible
                if (!androidNativeFoodAggregatorHomeScreen.isCategoryFilterDisplayed(categoryIdStr) || isElementPartiallyDisplayed(androidDriver
                        , androidNativeFoodAggregatorHomeScreen.getCategoryFilterUiElement(categoryIdStr), "right", 0.05)
                        || !androidNativeFoodAggregatorHomeScreen.isCategoryFilterNameDisplayed(categoryIdStr)) {

                    // Targeted scroll to find the specific category by ID
                    scrollUntilACertainElementIsFound(androidDriver,
                            androidNativeFoodAggregatorHomeScreen.getFilterScrollableContainer(),
                            "right",
                            "id=" + androidNativeFoodAggregatorHomeScreen.getCategoryFilterIdSelector(categoryIdStr));

                    // Capture visible categories after scrolling
                    androidNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleCategories(discoveredCategoryIds);
                }

                // Validate the target category
                if (androidNativeFoodAggregatorHomeScreen.isCategoryFilterDisplayed(categoryIdStr)) {
                    String displayedName;

                    if (androidNativeFoodAggregatorHomeScreen.isCategoryFilterNameDisplayed(categoryIdStr)) {
                        displayedName = androidNativeFoodAggregatorHomeScreen.extractCategoryFilterName(categoryIdStr);

                        if (!displayedName.equalsIgnoreCase(categoryName.trim())) {
                            validationResults.setResult(false);
                            validationResults.addALogToValidationResults("Category Filter with ID: " + categoryId
                                    + " has incorrect name displayed. Expected: \"" + categoryName.trim()
                                    + "\", Actual: \"" + displayedName + "\"\n");
                        }
                    } else scrollUntilACertainElementIsFound(androidDriver,
                            androidNativeFoodAggregatorHomeScreen.getFilterScrollableContainer(),
                            "right",
                            "id="+androidNativeFoodAggregatorHomeScreen.getCategoryFilterNameIdSelector(categoryIdStr));
                } else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("Category Filter with ID: " + categoryId
                            + " and name \"" + categoryName.trim() + "\" is not displayed\n");
                }
            }

            // Check for unexpected categories that were discovered during scrolling
            Set<String> unexpectedIds = new HashSet<>(discoveredCategoryIds);
            unexpectedIds.removeAll(expectedCategoryIds);

            if (!unexpectedIds.isEmpty()) {
                validationResults.setResult(false);
                for (String unexpectedId : unexpectedIds) {
                    // Get name only when needed for reporting
                    String unexpectedName = "Unknown";
                    try {
                        unexpectedName = androidNativeFoodAggregatorHomeScreen.extractCategoryFilterName(unexpectedId);
                    } catch (Exception e) {
                        // Keep "Unknown" if name retrieval fails
                    }

                    validationResults.addALogToValidationResults(
                            "UNEXPECTED category found - ID: " + unexpectedId +
                                    ", Name: \"" + unexpectedName + "\"\n");
                }
                validationResults.addALogToValidationResults(
                        "Total unexpected categories found: " + unexpectedIds.size() + "\n");
            }

            // Scroll back to the first category (direct access, no mapping needed)
            if (!expectedCategories.isEmpty()) {
                String firstCategoryId = String.valueOf(expectedCategories.getFirst().getYeloId());
                scrollUntilACertainElementIsFound(androidDriver,
                        androidNativeFoodAggregatorHomeScreen.getFilterScrollableContainer(),
                        "left",
                        "id="+androidNativeFoodAggregatorHomeScreen.getCategoryFilterIdSelector(firstCategoryId));

                // Final capture after scrolling back
                androidNativeFoodAggregatorHomeScreen.captureCurrentlyVisibleCategories(discoveredCategoryIds);
            }

        } catch (Exception e) {
            validationResults.setResult(false);
            validationResults.addALogToValidationResults("Error during category validation: " + e.getMessage() + "\n");
        }

        return validationResults;
    }

    public ValidationResults progressiveScrollAndVerifyAllRestaurants(
            AndroidDriver driver,
            AndroidNativeFoodAggregatorHomeScreen screen,
            List<Restaurant> allRestaurants,
            RestaurantVerifier verifier,
            String contextLogMessage
    ) {
        ValidationResults validationResults = new ValidationResults();

        Set<String> verifiedRestaurants = new HashSet<>();
        Set<String> expectedRestaurantIds = allRestaurants.stream()
                .map(r -> String.valueOf(r.getYeloId()))
                .collect(Collectors.toSet());

        try {
            boolean canScrollMore = true;
            int scrollAttempts = 0;
            int maxScrollAttempts = 50;

            while (canScrollMore && scrollAttempts < maxScrollAttempts && verifiedRestaurants.size() < expectedRestaurantIds.size()) {
                scrollAttempts++;

                Set<String> currentlyVisible = screen.getCurrentlyVisibleRestaurants();

                for (String restaurantId : currentlyVisible) {
                    if (expectedRestaurantIds.contains(restaurantId) && !verifiedRestaurants.contains(restaurantId)) {

                        // ✅ Lookup restaurant to get its name for logging
                        Restaurant restaurant = allRestaurants.stream()
                                .filter(r -> String.valueOf(r.getYeloId()).equals(restaurantId))
                                .findFirst()
                                .orElse(null);

                        boolean passed = verifier.verify(restaurantId);

                        if (!passed) {
                            validationResults.setResult(false);
                            validationResults.addALogToValidationResults("❌ Verification failed for restaurant ID: " + restaurantId + "\n");
                        } else {
                            if (restaurant != null) {
                                logger.info("Passed verification for restaurant: {} (ID: {})", restaurant.getName(), restaurantId);
                            } else {
                                logger.info("Something is wrong with the verification for restaurant ID: {}", restaurantId);
                            }
                        }

                        verifiedRestaurants.add(restaurantId);
                    }
                }

                Thread.sleep(300);
                canScrollMore = performIncrementalScroll(driver, screen.getRestaurantsScrollableContainer(), "down", screen);
                Thread.sleep(300);
            }

            Set<String> notFound = new HashSet<>(expectedRestaurantIds);
            notFound.removeAll(verifiedRestaurants);
            for (String id : notFound) {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("⚠️ Could not find restaurant during scrolling: " + id + "\n");
            }

            logger.info("📊 {} Summary: {}/{} restaurants verified",
                    contextLogMessage, verifiedRestaurants.size(), expectedRestaurantIds.size());

        } catch (Exception e) {
            validationResults.setResult(false);
            validationResults.addALogToValidationResults("❌ Error during verification: " + e.getMessage() + "\n");
        }

        return validationResults;
    }

/**
     * Performs a small incremental scroll to reveal more content
     * @return true if scroll was successful and more content might be available, false if at end
     */
    private boolean performIncrementalScroll(AndroidDriver driver, WebElement scrollView, String direction, AndroidNativeFoodAggregatorHomeScreen screen) {
        try {
            // Get current scroll position or content before scroll
            String beforeScrollState = getScrollState(screen.getRestaurantsScrollableContainer(),screen);

            // Perform small scroll action
            // Adjust scroll distance as needed - smaller scrolls for more precise control
            driver.findElement(AppiumBy.androidUIAutomator(
                    "new UiScrollable(new UiSelector().scrollable(true))" +
                            ".scrollForward()"));

            Thread.sleep(200); // Brief pause for scroll to complete

            // Check if scroll position changed
            String afterScrollState = getScrollState(screen.getRestaurantsScrollableContainer(), screen);

            return !beforeScrollState.equals(afterScrollState);

        } catch (Exception e) {
            logger.warn("Error during incremental scroll: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Helper method to get current scroll state for comparison
     */
    private String getScrollState(WebElement scrollView, AndroidNativeFoodAggregatorHomeScreen screen) {
        try {
            Set<String> visibleRestaurants =screen.getCurrentlyVisibleRestaurants();
            if (visibleRestaurants.isEmpty()) {
                return "empty";
            }

            // Convert to sorted list to get first and last
            List<String> sortedIds = new ArrayList<>(visibleRestaurants);
            Collections.sort(sortedIds);

            String firstVisible = sortedIds.get(0);
            String lastVisible = sortedIds.get(sortedIds.size() - 1);

            return firstVisible + "_" + lastVisible + "_" + visibleRestaurants.size();
        } catch (Exception e) {
            return "unknown_" + System.currentTimeMillis();
        }
    }
}
