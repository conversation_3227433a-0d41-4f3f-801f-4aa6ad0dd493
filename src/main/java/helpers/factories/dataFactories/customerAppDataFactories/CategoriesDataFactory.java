package helpers.factories.dataFactories.customerAppDataFactories;

import helpers.BaseHelper;
import helpers.apiClients.mobileApiClients.MobileWarehousesApiClient;
import models.Category;
import models.Configs;
import models.Warehouse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class CategoriesDataFactory extends BaseHelper {
    public CategoriesDataFactory(Configs configs) {
        this.configs = configs;
        mobileWarehousesApiClient = new MobileWarehousesApiClient(this.configs);
    }

    private final Configs configs;
    private final MobileWarehousesApiClient mobileWarehousesApiClient;
    private final Logger logger = LoggerFactory.getLogger(CategoriesDataFactory.class);

    public List<Category> buildCategoriesListInWarehouse(Warehouse warehouse, String nowTomorrowType) {
        switch (nowTomorrowType.toLowerCase()){
            case "later", "tomorrow" -> {
                return mobileWarehousesApiClient.getCategoriesByWarehouse(warehouse.getId(), "later");
            }
            default -> {
                return mobileWarehousesApiClient.getCategoriesByWarehouse(warehouse.getId(), "now");
            }
        }
    }
}
