package helpers.factories.dataFactories.testSessionsDataFactories;

import helpers.factories.dataFactories.customerAppDataFactories.ProductsDataFactory;
import models.*;

import java.util.ArrayList;
import java.util.List;

public class CustomerAppTestSessionFactory {
    public CustomerAppTestSessionFactory(Configs configs) {
        this.configs = configs;
        this.productsDataFactory = new ProductsDataFactory(this.configs);
    }

    private Configs configs;
    private ProductsDataFactory productsDataFactory;

    public CustomerAppTestSession buildRandomCategoryWithExclusiveDeliveryProductsInSubCategories(
            CustomerAppTestSession customerAppTestSession,
            List<Category> categories,
            String nowTomorrowType) {
        for (Category c : categories) {
            for (Category sc : c.getSubCategories()) {
                switch (nowTomorrowType.toLowerCase()){
                    case "tomorrow", "later" -> {
                        if (sc.getLaterProductsExclusive().size() > 0) {
                            customerAppTestSession.setTomorrowOnlyCategory(c);
                            customerAppTestSession.setTomorrowOnlySubcategory(sc);
                            customerAppTestSession.setTomorrowOnlyProduct(sc.getLaterProductsExclusive().get(0));
                            return customerAppTestSession;
                        }
                    }
                    default -> {
                        if (sc.getNowProductsExclusive().size() > 0) {
                            customerAppTestSession.setNowOnlyCategory(c);
                            customerAppTestSession.setNowOnlySubCategory(sc);
                            customerAppTestSession.setNowOnlyProduct(sc.getNowProductsExclusive().get(0));
                            return customerAppTestSession;
                        }
                    }
                }
            }
        }
        return customerAppTestSession;
    }

    public CustomerAppTestSession buildRandomCategoryWithSingleProductInSubCategories(
            CustomerAppTestSession customerAppTestSession) {
        List<Product> allSingleProducts = new ArrayList<>();
        List<Product> allSingleProductsWithPositiveStock = new ArrayList<>();

        for (Category c : customerAppTestSession.getNowCategoriesInWarehouse()) {
            for (Category sc : c.getSubCategories()) {
                if (!sc.getNowProductsInclusive().isEmpty()) {
                    //Define only single products
                    sc.setSingleProducts(productsDataFactory.identifySingleOnlyProducts(sc.getNowProductsInclusive()));

                    // Add single products from the current subcategory to the list of all single products
                    allSingleProducts.addAll(sc.getSingleProducts());
                }
            }
        }
        // Set all single products found in the categories
        for (Product p : allSingleProducts){
            if (p.getStock() >= 1)
                allSingleProductsWithPositiveStock.add(p);
        }
        customerAppTestSession.setSingleOnlyProducts(allSingleProductsWithPositiveStock);

        return customerAppTestSession;
    }

    public CustomerAppTestSession buildRandomCategoryWithCustomizableProductInSubCategories(
            CustomerAppTestSession customerAppTestSession) {
        List<Product> allCustomizableProducts = new ArrayList<>();
        List<Product> customizableProductsWithPositiveStock = new ArrayList<>();

        for (Category c : customerAppTestSession.getNowCategoriesInWarehouse()) {
            for (Category sc : c.getSubCategories()) {
                if (!sc.getNowProductsInclusive().isEmpty()) {
                    //Define only customizable products
                    sc.setCustomizableProducts(productsDataFactory.identifyCustomizableOnlyProducts(sc.getNowProductsInclusive()));

                    // Add customizable products from the current subcategory to the list of all customizable products
                    allCustomizableProducts.addAll(sc.getCustomizableProducts());
                }
            }
        }
        // Set all customizable products found in the categories
        for (Product p : allCustomizableProducts){
            if (p.getStock() >= 1)
                customizableProductsWithPositiveStock.add(p);
        }
        customerAppTestSession.setCustomizableOnlyProducts(customizableProductsWithPositiveStock);

        return customerAppTestSession;
    }

    public CustomerAppTestSession buildRandomCategoryWithBundleProductInSubCategories(
            CustomerAppTestSession customerAppTestSession) {
        List<Product> allBundleProducts = new ArrayList<>();
        List<Product> bundleProductsWithPositiveStock = new ArrayList<>();

        for (Category c : customerAppTestSession.getNowCategoriesInWarehouse()) {
            for (Category sc : c.getSubCategories()) {
                if (!sc.getNowProductsInclusive().isEmpty()) {
                    //Define only bundle products
                    sc.setBundleProducts(productsDataFactory.identifyBundleOnlyProducts(sc.getNowProductsInclusive()));

                    // Add bundle products from the current subcategory to the list of all bundle products
                    allBundleProducts.addAll(sc.getBundleProducts());
                }
            }
        }

        // Set positive  bundle products found in the categories
        for (Product p : allBundleProducts){
            if (p.getStock() >= 1)
                bundleProductsWithPositiveStock.add(p);
        }

        customerAppTestSession.setBundleOnlyProducts(bundleProductsWithPositiveStock);

        return customerAppTestSession;
    }

    public CustomerAppTestSession buildRandomCategoryWithOutOfStockProductInSubCategories(
            CustomerAppTestSession customerAppTestSession) {
        for (Category c : customerAppTestSession.getNowCategoriesInWarehouse()) {
            for (Category sc : c.getSubCategories()) {
                for (Product p : sc.getNowProductsInclusive()) {
                    if (p.getNowStock() == (double) 0) {
                        customerAppTestSession.setOutOfStockServeType("now");
                        customerAppTestSession.setOutOfStockOnlyCategory(c);
                        customerAppTestSession.setOutOfStockOnlySubcategory(sc);
                        customerAppTestSession.setOutOfStockOnlyProduct(p);
                        return customerAppTestSession;
                    }
                }
            }
        }
        for (Category c : customerAppTestSession.getTomorrowCategoriesInWarehouse()) {
            for (Category sc : c.getSubCategories()) {
                for (Product p : sc.getLaterProductsInclusive()) {
                    if (p.getStock() == (double) 0) {
                        customerAppTestSession.setOutOfStockServeType("later");
                        customerAppTestSession.setOutOfStockOnlyCategory(c);
                        customerAppTestSession.setOutOfStockOnlySubcategory(sc);
                        customerAppTestSession.setOutOfStockOnlyProduct(p);
                        return customerAppTestSession;
                    }
                }
            }
        }
        return customerAppTestSession;
    }

    public CustomerAppTestSession buildRandomCategoryWithNowProductThatHasPositiveStock(
            CustomerAppTestSession customerAppTestSession) {
        for (Category c : customerAppTestSession.getNowCategoriesInWarehouse()) {
            for (Category sc : c.getSubCategories()) {
                if (sc.getNowProductsWithPositiveStocks() != null
                        && !sc.getNowProductsWithPositiveStocks().isEmpty()) {
                    customerAppTestSession.setNowCategoryWithPositiveStock(c);
                    customerAppTestSession.setNowSubcategoryWithPositiveStock(sc);
                    customerAppTestSession.setNowProductWithPositiveStock(
                            sc.getNowProductsWithPositiveStocks().getFirst());
                    return customerAppTestSession;
                }
            }
        }
        return customerAppTestSession;
    }

    public CustomerAppTestSession buildRandomCategoryWithSingleAndBundleProductsInSubCategories(
            CustomerAppTestSession customerAppTestSession) {
        customerAppTestSession.setSingleAndBundleProductsList(
                productsDataFactory.identifySingleAndBundleProducts
                        (customerAppTestSession.getBundleOnlyProducts()
                                , customerAppTestSession.getSingleOnlyProducts()));

        return customerAppTestSession;
    }

    public CustomerAppTestSession buildRandomCategoryWithCustomizableAndNonCustomizableProductsInSubCategories(
            CustomerAppTestSession customerAppTestSession) {
        customerAppTestSession.setCustomizableAndNonCustomizableProducts(
                productsDataFactory.identifyCustomizableAndNonCustomizableProducts
                        (customerAppTestSession.getCustomizableOnlyProducts()
                                , customerAppTestSession.getSingleOnlyProducts()));

        return customerAppTestSession;
    }

    public CustomerAppTestSession buildRandomCategoryWithLaterProductThatHasPositiveStock(
            CustomerAppTestSession customerAppTestSession) {
        for (Category c : customerAppTestSession.getTomorrowCategoriesInWarehouse()) {
            for (Category sc : c.getSubCategories()) {
                if (sc.getLaterProductsWithPositiveStocks() != null
                        && !sc.getLaterProductsWithPositiveStocks().isEmpty()) {
                    customerAppTestSession.setTomorrowCategoryWithPositiveStock(c);
                    customerAppTestSession.setTomorrowSubCategoryWithPositiveStock(sc);
                    customerAppTestSession.setTomorrowProductWithPositiveStock(
                            sc.getLaterProductsWithPositiveStocks().getFirst());
                    return customerAppTestSession;
                }
            }
        }
        return customerAppTestSession;
    }

    public CustomerAppTestSession buildPositiveStockListsAcrossWarehouse(
            CustomerAppTestSession customerAppTestSession){
        customerAppTestSession.getTestWarehouse().setNowProductsWithPositiveStock(new ArrayList<>());
        customerAppTestSession.getTestWarehouse().setLaterProductsWithPositiveStock(new ArrayList<>());
        for (Category c : customerAppTestSession.getNowCategoriesInWarehouse()){
            for (Category sc : c.getSubCategories()){
                if (sc.getNowProductsWithPositiveStocks() != null
                        && !sc.getNowProductsWithPositiveStocks().isEmpty())
                    customerAppTestSession.getTestWarehouse().getNowProductsWithPositiveStock()
                            .addAll(sc.getNowProductsWithPositiveStocks());
            }
        }
        for (Category c : customerAppTestSession.getTomorrowCategoriesInWarehouse()){
            for (Category sc : c.getSubCategories()){
                if (sc.getLaterProductsWithPositiveStocks() != null
                        && !sc.getLaterProductsWithPositiveStocks().isEmpty())
                    customerAppTestSession.getTestWarehouse().getLaterProductsWithPositiveStock()
                            .addAll(sc.getLaterProductsWithPositiveStocks());
            }
        }
        return customerAppTestSession;
    }

    public CustomerAppTestSession buildSingleProductsWithoutDiscountList(
            CustomerAppTestSession customerAppTestSession){
        List<Product> singleProductsWithOutDiscount = productsDataFactory.identifyProductsWithoutDiscount(
                customerAppTestSession.getSingleOnlyProducts()
                ,customerAppTestSession.getTestWarehouse().getId());

        customerAppTestSession.setSingleProductsWithoutDiscount(singleProductsWithOutDiscount);
        return customerAppTestSession;
    }

    public CustomerAppTestSession buildSingleProductsWithSalePriceList(
            CustomerAppTestSession customerAppTestSession){
        List<Product> singleProductsWithSalePrice = productsDataFactory.identifyProductsWithSalePrice(
                customerAppTestSession.getSingleOnlyProducts()
                ,customerAppTestSession.getTestWarehouse().getId());

        customerAppTestSession.setSingleProductsWithSalePrice(singleProductsWithSalePrice);
        return customerAppTestSession;
    }

    public CustomerAppTestSession buildSingleProductsWithExtraSalePriceList(
            CustomerAppTestSession customerAppTestSession){
        List<Product> singleProductsWithExtraSalePrice = productsDataFactory.identifyProductsWithExtraSalePrice(
                customerAppTestSession.getSingleOnlyProducts()
                ,customerAppTestSession.getTestWarehouse().getId());

        customerAppTestSession.setSingleProductsWithExtraSalePrice(singleProductsWithExtraSalePrice);
        return customerAppTestSession;
    }

    public CustomerAppTestSession buildBundleProductsWithoutDiscountList(
            CustomerAppTestSession customerAppTestSession){
        List<Product> bundleProductsWithOutDiscount = productsDataFactory.identifyProductsWithoutDiscount(
                customerAppTestSession.getBundleOnlyProducts()
                ,customerAppTestSession.getTestWarehouse().getId());

        customerAppTestSession.setBundleProductsWithoutDiscount(bundleProductsWithOutDiscount);
        return customerAppTestSession;
    }

    public CustomerAppTestSession buildBundleProductsWithSalePriceList(
            CustomerAppTestSession customerAppTestSession){
        List<Product> bundleProductsWithSalePrice = productsDataFactory.identifyProductsWithSalePrice(
                customerAppTestSession.getBundleOnlyProducts()
                ,customerAppTestSession.getTestWarehouse().getId());

        customerAppTestSession.setBundleProductsWithSalePrice(bundleProductsWithSalePrice);
        return customerAppTestSession;
    }

    public CustomerAppTestSession buildSingleAndBundleProductsWithoutDiscount(
            CustomerAppTestSession customerAppTestSession){
        List<Product> singleAndBundleProductsWithOutDiscount = productsDataFactory.identifyProductsWithoutDiscount(
                customerAppTestSession.getSingleAndBundleProductsList()
                ,customerAppTestSession.getTestWarehouse().getId());

        customerAppTestSession.setSingleAndBundleProductsWithoutDiscount(
                singleAndBundleProductsWithOutDiscount);
        return customerAppTestSession;
    }

    public CustomerAppTestSession buildSingleAndBundleProductsWithSalePrice(
            CustomerAppTestSession customerAppTestSession){
        List<Product> singleAndBundleProductsWithSalePrice = productsDataFactory.identifyProductsWithSalePrice(
                customerAppTestSession.getSingleAndBundleProductsList()
                ,customerAppTestSession.getTestWarehouse().getId());

        customerAppTestSession.setSingleAndBundleProductsWithSalePrice(
                singleAndBundleProductsWithSalePrice);
        return customerAppTestSession;
    }
}
