package modals.mainAdminPortal;

import modals.BaseWebPage;
import models.TestData;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class LoginPage extends BaseWebPage {
    public LoginPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    @FindBy(id = "MazPhoneNumberInput-1_phone_number")
    WebElement phoneNumberField;

    @FindBy(xpath = "//div[@id=\"loginForm\"]/button[@class=\"btn btn-block btn-breadfast\"]")
    WebElement submitBtn;

    @FindBy(xpath = "//a[@data-provider=\"google\"]")
    WebElement continueWithGoogleBtn;

    @FindBy(xpath = "//div[@id=\"login\"]/p[@class=\"text-left\"]/a[text()=\"Login with another phone number\"]")
    WebElement loginWithAnotherPhoneNumberLink;

    String pagePath = "login/";

    String bypassLoginScript = "$.ajax({url: ajaxurl + '?action=u_user_login'" +
            ", type: 'post'," +
            "data: { phone: '%s', password: '%s' }})";

    @FindBy(id = "MazPhoneNumberInput-1_country_selector")
    WebElement changeCountryCodeDropdown;

    String selectCountryListItem ="//button[contains(@class, 'country-selector__list__item')]//div[contains(@class, 'dots-text') and contains(text(), '%s')]";

    public void enterPhoneNumber(String phoneNumber){
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberField))
                .sendKeys(phoneNumber);
    }

    public boolean isPhoneNumberEnteredCorrectly(String phoneNumber){
        try {
            wait.until(ExpectedConditions
                    .textToBePresentInElementValue(phoneNumberField, phoneNumber));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public void pressSubmitBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(submitBtn))
                .click();
    }

    public void pressGoogleLoginBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(continueWithGoogleBtn))
                .click();
    }

    public boolean isGoogleLoginBtnDisplayed(){
        try {
            wait.until(ExpectedConditions.visibilityOf(continueWithGoogleBtn));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public boolean isGoogleLoginBtnNotDisplayed(){
        try {
            wait.until(ExpectedConditions.invisibilityOf(continueWithGoogleBtn));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public void pressLoginWithAnotherPhoneNumberLink(){
        wait.until(ExpectedConditions.elementToBeClickable(loginWithAnotherPhoneNumberLink))
                .click();
    }

    public void goToLoginPage(){
        visitASubPage(pagePath);
    }

    public void byPassGoogleLogin(String phoneNumber, String byPassPassword){
        executeJavaScriptCode(String.format(bypassLoginScript, phoneNumber, byPassPassword));
        webDriver.navigate().to(webDriver.getCurrentUrl().replaceFirst("(?i)/login/?$", "/"));
    }

    public void loginWithByPassScript(String phoneNumber, String byPassScriptPwd) {

        if (!webDriver.getCurrentUrl().contains("ksa")) {
            goToLoginPage();
        }
        enterPhoneNumber(phoneNumber);
        pressSubmitBtn();
        isGoogleLoginBtnDisplayed();
        byPassGoogleLogin(phoneNumber, byPassScriptPwd);
    }
    public void changeCountryCode() {

        if (webDriver.getCurrentUrl().contains("ksa")) {
            wait.until(ExpectedConditions.elementToBeClickable(changeCountryCodeDropdown)).click();
            wait.until(ExpectedConditions.elementToBeClickable(
                    By.xpath(String.format(selectCountryListItem, "Egypt")))).click();        }
    }
}
