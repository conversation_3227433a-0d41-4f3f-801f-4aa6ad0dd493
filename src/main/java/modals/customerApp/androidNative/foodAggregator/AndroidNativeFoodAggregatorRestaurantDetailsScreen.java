package modals.customerApp.androidNative.foodAggregator;
import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeFoodAggregatorRestaurantDetailsScreen extends BaseAndroidScreen {
    public AndroidNativeFoodAggregatorRestaurantDetailsScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver,this);
    }

    @FindBy(xpath = "")
    WebElement restaurantName;

    @FindBy(id = "restaurantDetailsBackBtn")
    WebElement backBtn;

    @FindBy(id = "restaurantDetailsCartBtn")
    WebElement cartIcon;

    @FindBy(xpath = "")
    WebElement restaurantCategory;

    @FindBy(xpath = "")
    WebElement restaurantRating;

    @FindBy(xpath = "")
    WebElement restaurantDetailsSection;

    @FindBy(id = "restaurantDetailsBtn")
    WebElement expandRestaurantDetailsBtn;

    @FindBy(xpath = "")
    WebElement restaurantDeliveryFees;

    @FindBy(xpath = "")
    WebElement restaurantDeliverySlot;

    @FindBy(xpath = "")
    WebElement restaurantDeliveredByTag;

    @FindBy(xpath = "")
    WebElement restaurantCategories;

    @FindBy(xpath = "")
    WebElement restaurantProductName;

    @FindBy(xpath = "")
    WebElement restaurantProductPrice;

    @FindBy(id = "product_incrementQtyBtn")
    WebElement firstAddToCartBtn;

    @FindBy(id = "product_decrementQtyBtn")
    WebElement firstDecreaseQtyBtn;

    @FindBy(id = "productCartQty")
    WebElement firstProductCartQty;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'This item requires a separate order.')]")
    WebElement cartTypeErrorMsg;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Start new order')]")
    WebElement startNewOrderBtn;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Keep current cart')]")
    WebElement keepCurrentCartBtn;

    public boolean isPageDisplayed() {
        return isElementDisplayed(expandRestaurantDetailsBtn);
    }

    public void pressFirstAddToCartBtn(){
        wait.until(ExpectedConditions.visibilityOf(firstAddToCartBtn))
                .click();
    }

    public void pressFirstDecreaseQtyBtn(){
        wait.until(ExpectedConditions.visibilityOf(firstDecreaseQtyBtn))
                .click();
    }

    public boolean isRestaurantBackBtnDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(backBtn)).isDisplayed();
    }

    //Assert here on categories
    public boolean isRestaurantCategoriesDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantCategories)).isDisplayed();
    }

    public boolean isRestaurantCategoryDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantCategory)).isDisplayed();
    }

    public boolean isRestaurantSlotDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantDeliverySlot)).isDisplayed();
    }

    public boolean isRestaurantFessDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantDeliveryFees)).isDisplayed();
    }

    public boolean isRestaurantDeliveredByTagDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantDeliveredByTag)).isDisplayed();
    }

    public boolean isRestaurantCartIconDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(cartIcon)).isDisplayed();
    }

    public boolean isRestaurantRatingDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantRating)).isDisplayed();
    }

    public boolean isRestaurantDetailsSectionDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantDetailsSection)).isDisplayed();
    }

    public void pressRestaurantDetailsSection()
    {
         wait.until(ExpectedConditions.visibilityOf(restaurantDetailsSection)).click();
    }

    public void pressBackBtn()
    {
        wait.until(ExpectedConditions.visibilityOf(backBtn)).click();
    }

    public void goToCartScreen() {
        wait.until(ExpectedConditions.visibilityOf(cartIcon)).click();
    }

    public boolean isFirstProductAddedToCart(){
        return isElementDisplayed(firstProductCartQty);
    }

    public String getCurrentQtyOfFirstProductInCart() {
        return wait.until(ExpectedConditions.visibilityOf(firstProductCartQty)).getText();
    }

    public boolean isCartTypeErrorMsgDisplayed() {
        return isElementDisplayed(cartTypeErrorMsg);
    }

    public void pressStartNewOrderBtn() {
        wait.until(ExpectedConditions.visibilityOf(startNewOrderBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(startNewOrderBtn));
    }

    public void pressKeepCurrentCartBtn() {
        wait.until(ExpectedConditions.visibilityOf(keepCurrentCartBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(keepCurrentCartBtn));
    }
}

