package modals.customerApp.androidNative.androidNativeHomePage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.*;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class AndroidNativeHomeScreen extends BaseAndroidScreen {

    public AndroidNativeHomeScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.View[@content-desc='bottomBar_home_btn']")
    WebElement homePageBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='bottomBar_more_btn']")
    WebElement morePageBtn;

    @FindBy(xpath = "//androidx.compose.ui.platform.ComposeView/android.view.View/android.view.View/android.view.View[2]/android.view.View[4]")
    WebElement mainCategoryContainer;

    @FindBy(xpath = "(//android.widget.TextView[contains(@index,'1')])[1]")
    WebElement topPicksTitle;

    @FindBy(xpath = "//android.widget.TextView[@text=\"View all\"]")
    WebElement topPicksViewAllLink;

    String categoryCardSelector = "categoryItem-%s";

    String categoryNameSelector = "categoryItem-%s_name";

    String categoryCardXpathSelector = "//android.view.View[@content-desc='%s']";

    @FindBy (xpath = "//android.widget.Button[@content-desc='miniTracker_activeOrder_btn']")
    WebElement expandMiniTracker;

    @FindBy (xpath = "//android.widget.TextView[@text='View order details']")
    WebElement viewOrderDetailsBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Order details']")
    WebElement orderDetailsHeader;

    @FindBy(xpath = "//android.widget.Button")
    WebElement closeExpandedMiniTracker;

    @FindBy(xpath = "//android.widget.TextView[@text='Preparing your order']")
    WebElement preparingYourOrderMiniTracking;

    @FindBy(xpath = "//android.widget.TextView[contains(text(),'Order')]")
    WebElement orderStatus;

    @FindBy(xpath = "//android.widget.TextView[@text='Order placed successfully']")
    WebElement placeOrderMiniTracking;

    @FindBy(xpath = "//android.widget.TextView[@text=\"We're currently busy\"]")
    WebElement busyModalHeader;

    @FindBy(xpath = "//androidx.compose.ui.platform.ComposeView/android.view.View/android.view.View/android.view.View[2]/android.view.View[1]/android.view.View[2]")
    WebElement poultryBanner;

    @FindBy(xpath = "//android.widget.TextView[@text='The spotlight']")
    WebElement collectionsTitle;

    @FindBy(xpath = "//android.widget.TextView[@text='The spotlight']/following-sibling::*[2]/android.view.View[1]")
    WebElement firstAvailableCollection;

    // Rating popup locators - updated with more generic selectors for better reliability
    @FindBy(xpath = "//android.widget.ScrollView[@content-desc=\"ratingBottomSheet_contentsContainer\"]")
    WebElement rateOrderRatingPopup;

    String scrollableContentContainer = "//androidx.compose.ui.platform.ComposeView/android.view.View/android.view.View/android.view.View[2]";

    public boolean isPageDisplayed(){
        return isElementDisplayed(homePageBtn) || isElementDisplayed(busyModalHeader) || isElementDisplayed(rateOrderRatingPopup);
    }

    public boolean isPageDismissed(){
        return isElementHidden(homePageBtn);
    }

    public void pressMoreBtn(){
        wait.until(ExpectedConditions.visibilityOf(morePageBtn))
                .click();
    }

    public boolean isMainCategoryListDisplayed() {
        return isElementDisplayed(wait.until(ExpectedConditions.visibilityOf(mainCategoryContainer)));
    }

    public String getCategoryContentDescription(String categoryId){
        return String.format(categoryCardSelector, categoryId);
    }

    public String getCategoryXpathSelector(String categoryId){
        return String.format(categoryCardXpathSelector, getCategoryContentDescription(categoryId));
    }

    public WebElement getCategoryUiElement(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(
                getCategoryXpathSelector(categoryId))));
    }

    public boolean isCategoryDisplayed(String categoryId){
        return isElementDisplayed(getCategoryUiElement(categoryId));
    }
    public WebElement getScrollableContentContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(scrollableContentContainer)));
        } catch (Exception e) {
            return null;
        }
    }
    public void pressCategoryById(String categoryId){
        getCategoryUiElement(categoryId)
                .click();
        wait.until(ExpectedConditions.invisibilityOfElementLocated(By.xpath(
                getCategoryXpathSelector(categoryId))));
    }

    public void clickMainCategoryListDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(mainCategoryContainer)).click();
    }

    public boolean isTopPicksTitleDisplayed(){return isElementDisplayed(topPicksTitle);}

    public boolean isViewAllTopPicksLinkDisplayed(){return isElementDisplayed(topPicksViewAllLink);}

    public void clickViewAllTopPicks(){
        wait.until(ExpectedConditions.visibilityOf(topPicksViewAllLink)).click();
    }

    public boolean placeOrderMiniTrackingIsDisplayed(){

        return isElementDisplayed(placeOrderMiniTracking);
    }

    public void clickExpandMiniTracker(){
        wait.until(ExpectedConditions.visibilityOf(expandMiniTracker)).click();
    }

    public void clickViewOrderDetailsBtn(){
        wait.until(ExpectedConditions.visibilityOf(viewOrderDetailsBtn)).click();
    }

    public void clickCloseExpandedMiniTracker(){
        wait.until(ExpectedConditions.visibilityOf(closeExpandedMiniTracker)).click();
    }

    public boolean isMiniTrackerDisplayed(){
        return isElementDisplayed(expandMiniTracker);
    }

    public void pressHomeBtn(){
        wait.until(ExpectedConditions.visibilityOf(homePageBtn)).click();
    }

    public boolean preparingYourOrderMiniTrackingIsDisplayed(){
        return isElementDisplayed(preparingYourOrderMiniTracking);
    }

    public String getOrderStatus() {
        return wait.until(ExpectedConditions.visibilityOf(orderStatus))
                .getText();
    }

    public boolean isMiniTrackerInvisible(){
        return wait.until(ExpectedConditions.invisibilityOf(viewOrderDetailsBtn));
    }

    public void pressPoultryBanner() {
        wait.until(ExpectedConditions.visibilityOf(poultryBanner)).click();
    }

    public boolean isCollectionsAvailable(){
        return isElementDisplayed(collectionsTitle);
    }

    public void pressFirstAvailableCollection(){
        wait.until(ExpectedConditions.visibilityOf(firstAvailableCollection)).click();
    }
}
