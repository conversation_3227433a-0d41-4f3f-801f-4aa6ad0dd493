package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class IosNativeCartScreen extends BaseIosScreen {
    public IosNativeCartScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='cart_checkout_btn']")
    WebElement goToCheckoutBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Cart']")
    WebElement cartScreen;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@value='High demand - No delivery slots available']")
    WebElement busyMessage;

    @FindBy(xpath = "//XCUIElementTypeStaticText[starts-with(@name, 'cartScreen_product_') " +
            "and substring(@name, string-length(@name) - 5) = '_title']")
    List<WebElement> productTitleElement;

    public boolean isPageDisplayed(){
        return isElementDisplayed(cartScreen);
    }

    public void pressGoToCheckoutBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(goToCheckoutBtn))
                .click();
    }

    public boolean isBusyMessageDisplayed(){
        return isElementDisplayed(busyMessage);
    }

    public int getCountOfProductsInCart() {
        return productTitleElement.size();
    }
}
