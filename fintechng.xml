<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="All Test Suite" parallel="methods" skipfailedinvocationcounts="true" thread-count="3" data-provider-thread-count="10">
    <listeners>
        <listener class-name="io.qameta.allure.testng.AllureTestNg"/>
<!--        <listener class-name="helpers.factories.AllureLogListener"/>-->
    </listeners>
    <test name="card">
        <groups>
            <run>
                <include name="card"/>
            </run>
        </groups>
        <classes>
            <class name="cardService.api.CardActivationTests"/>
        </classes>
    </test>
    <test name="payment">
        <groups>
            <run>
                <include name="payment"/>
            </run>
        </groups>
        <classes>
            <class name="cardService.api.CardActivationTests"/>
        </classes>
    </test>
</suite>