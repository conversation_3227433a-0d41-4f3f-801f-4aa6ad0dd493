<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="All Test Suite" parallel="methods" skipfailedinvocationcounts="true" thread-count="2" data-provider-thread-count="10">
    <listeners>
        <listener class-name="io.qameta.allure.testng.AllureTestNg"/>
<!--        <listener class-name="helpers.factories.AllureLogListener"/>-->
    </listeners>
    <test name="ops">
        <classes>
            <class name="pickerApp.api.UserTests"/>
        </classes>
    </test>
    <test name="logistics">
        <classes>
            <class name="fleetApp.android.LoginFlowTests"></class>
            <class name="fleetApp.android.SideMenuTests"></class>
        </classes>
    </test>
    <test name="inventory">
        <classes>
            <class name="stockTake.api.StockTakeApiTests"></class>
        </classes>
    </test>
    <test name="supply-demand">
        <classes>
            <class name="planningCenter.PlanningCenterTests"/>
            <class name="controlRoom.ControlRoomTests">
                <methods>
                    <exclude name="validateGroupByTimeslot"/>
                    <exclude name="validateAllOrdersAreDisplayed"/>
                    <exclude name="validateThatAssigningDaIsWorkingCorrectly"/>
                    <exclude name="validateAssigningPickerToAnOrderIsWorkingCorrectly"/>
                    <exclude name="validateFPDimmed"/>
                    <exclude name="checkCompletingOrderFromDetails"/>
                    <exclude name="validateFiltersPage"/>
                </methods>
            </class>
            <class name="midMileApp.api.authentication.MidMileApiTests"></class>
            <class name="midMileApp.api.authentication.OrdersTests"></class>
            <class name="midMileApp.android.authentication.LoginMobileTests"></class>
            <class name="midMileApp.android.authentication.ActionTests"></class>
        </classes>
    </test>
</suite>