name: Build and Test

on:
  push:
  pull_request:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select the target environment'
        required: true
        default: 'integration'
        type: choice
        options:
          - testing
          - integration
          - production
      test_country_code:
        description: 'Select the test country code'
        required: true
        default: 'EG'
        type: choice
        options:
          - EG
          - KSA

jobs:
  branch-name-check:
    runs-on: ubuntu-latest
    env:
      BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
    steps:
      - name: Check Branch Naming Convention
        run: |
          branch_name=$BRANCH_NAME
          current_year=$(date +'%Y')
          branch_pattern="^$current_year/sprintQ[0-9]+\.[0-9]+(/[-a-z0-9BPHPO]+)*$"
          echo "Current branch name is: $branch_name"
          echo "Expected pattern: $branch_pattern"
          if [ "$branch_name" == "main" ]; then
            echo "Branch name check passed for main branch."
          elif [[ ! $branch_name =~ $branch_pattern ]]; then
            echo "Error: Branch name '$branch_name' does not match the required naming convention."
            echo "Branch names should start with '$current_year/sprintQ<number>.<number>/' and contain only lowercase letters, numbers, Capital letter 'B', Capital letters 'PHPO' and hyphens."
            exit 1
          else
            echo "Branch name check passed."
          fi

  code-style-validation:
    runs-on: ubuntu-latest
    needs: branch-name-check
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 24
        uses: actions/setup-java@v4
        with:
          java-version: '24'
          distribution: 'oracle'
          cache: maven

      - name: Set up Maven
        uses: stCarolas/setup-maven@v5
        with:
          maven-version: 3.9.8

      - name: Run Checkstyle
        run: |
          set +e  # Prevent script from stopping immediately on error
          mvn checkstyle:check
          CHECKSTYLE_EXIT_CODE=$?
          set -e  # Re-enable stopping the script on error
          if [ $CHECKSTYLE_EXIT_CODE -ne 0 ]; then
            echo "There are errors in the code style checks."
            echo "Please run 'mvn checkstyle:check' on your local machine to see the details of the errors."
            exit $CHECKSTYLE_EXIT_CODE
          fi

  build-and-test:
    name: General Test - ${{ github.event.inputs.environment || 'integration' }}
    runs-on: ubuntu-latest
    needs: code-style-validation
    environment: ${{ github.event.inputs.environment || 'integration' }} # Default to 'TESTING' if not set
    env:
      SELECTED_ENV: ${{ github.event.inputs.environment || 'integration' }}
      TEST_COUNTRY_CODE: ${{ github.event.inputs.test_country_code || 'EG' }} # Default to 'EG' if not set

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 24
        uses: actions/setup-java@v4
        with:
          java-version: '24'
          distribution: 'oracle'
          cache: maven

      - name: Set up Maven
        uses: stCarolas/setup-maven@v5
        with:
          maven-version: 3.9.8

      - name: Install Node.js and npm
        uses: actions/setup-node@v4
        with:
          node-version: '22.18.0'

      - name: Copy config files
        run: |
            cp resources/environments/config.properties.example resources/environments/config_${{ env.SELECTED_ENV }}.properties
            cp resources/environments/webConfig.properties.example resources/environments/webConfig.properties
            cp resources/environments/cardServiceConfigs.properties.example resources/environments/cardServiceConfigs_${{ env.SELECTED_ENV }}.properties
            cp resources/environments/iPhone_14.properties.example resources/environments/iPhone_14.properties
            cp resources/environments/Pixel_2.properties.example resources/environments/Pixel_2.properties
            cp resources/environments/cardServiceEncryptionPublicKey.pub.example resources/environments/cardServiceEncryptionPublicKey.pub
            cp resources/environments/dbSshKey.example resources/environments/dbSshKey

      - name: Set Test Mobile Number
        id: set_mobile_number
        run: |
          if [ "$TEST_COUNTRY_CODE" == "EG" ]; then
            echo "test_mobile_number=+20" >> $GITHUB_ENV
          else
            echo "test_mobile_number=+966" >> $GITHUB_ENV
          fi

      - name: Select Test Mobile Company
        id: select_mobile_company
        run: |
          if [ "$TEST_COUNTRY_CODE" == "EG" ]; then
            echo "mobile_company=Etisalat" >> $GITHUB_ENV
          else
            echo "mobile_company=Zain" >> $GITHUB_ENV
          fi

#      - name: Install QEMU
#        run: |
#          sudo apt-get update
#          sudo apt install -y qemu-kvm
#
#      - name: Download APK based on environment
#        env:
#          TESTING_LINK: ${{ vars.APK_DOWNLOAD_LINK }}
#        run: |
#          echo "Downloading APK for TESTING environment from $TESTING_LINK"
#          wget -q -O resources/builds/app.apk "$TESTING_LINK"
#
#      - name: Download IPA based on environment
#        env:
#          TESTING_LINK: ${{ vars.IPA_DOWNLOAD_LINK }}
#        run: |
#          echo "Downloading IPA for TESTING environment from $TESTING_LINK"
#          wget -q -O resources/builds/app.ipa "$TESTING_LINK"

      - name: Fetch Latest Stable Chrome Version
        run: |
          LATEST_STABLE_CHROME_VERSION=$(curl -s https://googlechromelabs.github.io/chrome-for-testing/LATEST_RELEASE_STABLE)
          echo "LATEST_STABLE_CHROME_VERSION=$LATEST_STABLE_CHROME_VERSION" >> $GITHUB_ENV

      - name: Download ChromeDriver Matching Latest Stable Chrome
        run: |
          CHROMEDRIVER_URL="https://storage.googleapis.com/chrome-for-testing-public/${{ env.LATEST_STABLE_CHROME_VERSION }}/linux64/chromedriver-linux64.zip"
          curl -s -o chromedriver.zip "$CHROMEDRIVER_URL"
          unzip -q chromedriver.zip -d resources/webDrivers
          mv resources/webDrivers/chromedriver-linux64/chromedriver resources/webDrivers/
          chmod +x resources/webDrivers/chromedriver
          ls -la resources/webDrivers/

      - name: Install OpenSSH client
        run: |
          sudo apt-get update
          sudo apt-get install -y openssh-client

      - name: Install MySQL client
        run: |
          sudo apt-get update
          sudo apt-get install -y mysql-client

      - name: Modify config.properties file
        run: |
          sed -i'' \
              -e "s/^testCountryCode=.*/testCountryCode=$TEST_COUNTRY_CODE/" \
              -e "s/^testMobileNumber=.*/testMobileNumber=${{ env.test_mobile_number }}/" \
              -e "s/^testMobileCompany=.*/testMobileCompany=${{ env.mobile_company }}/" \
              -e "s/^testFpName=.*/testFpName=${{ vars.FP_NAME }}/" \
              -e "s/^testFpDate=.*/testFpDate=${{ vars.FP_DATE }}/" \
              -e "s/^testLatitude=.*/testLatitude=${{ vars.WAREHOUSE_LATITUDE }}/" \
              -e "s/^testLongitude=.*/testLongitude=${{ vars.WAREHOUSE_LONGITUDE }}/" \
              -e "s|^testOrderInfo=.*|testOrderInfo=${{ vars.ORDER_INFO }}|" \
              -e "s/^adminBypassScriptPassword=.*/adminBypassScriptPassword=${{ secrets.ADMIN_BYPASS_SCRIPT_PASSWORD }}/" \
              -e "s/^adminGmailAddress=.*/adminGmailAddress=${{ secrets.ADMIN_GMAIL_ADDRESS }}/" \
              -e "s/^adminGmailPassword=.*/adminGmailPassword=${{ secrets.ADMIN_GMAIL_PASSWORD }}/" \
              -e "s/^midMilePassword=.*/midMilePassword=${{ secrets.MID_MILE_PASSWORD }}/" \
              -e "s/^ngrokAuthToken=.*/ngrokAuthToken=${{ secrets.NGROK_AUTH_TOKEN }}/" \
              -e "s/^pickerPassword=.*/pickerPassword=${{ secrets.PICKER_PASSWORD }}/" \
              -e "s/^slackApiToken=.*/slackApiToken=${{ secrets.SLACK_API_TOKEN }}/" \
              -e "s/^wpLoggedInCookieName=.*/wpLoggedInCookieName=${{ secrets.WP_LOGGED_IN_COOKIE_NAME }}/" \
              -e "s/^wpNodeAuthorizationCookieName=.*/wpNodeAuthorizationCookieName=${{ secrets.WP_NODE_AUTHORIZATION_COOKIE_NAME }}/" \
              -e "s/^wpSecCookieName=.*/wpSecCookieName=${{ secrets.WP_SEC_COOKIE_NAME }}/" \
              -e "s/^adminLocalPhoneNumber=.*/adminLocalPhoneNumber=${{ vars.ADMIN_LOCAL_PHONE_NUMBER }}/" \
              -e "s/^adminReferralCode=.*/adminReferralCode=${{ vars.ADMIN_REFERRAL_CODE }}/" \
              -e "s/^midMilePhoneNumber=.*/midMilePhoneNumber=${{ vars.MID_MILE_PHONE_NUMBER }}/" \
              -e "s/^pickerPhoneNumber=.*/pickerPhoneNumber=${{ vars.PICKER_PHONE_NUMBER }}/" \
              -e 's/^paymentServiceSecret=.*/paymentServiceSecret=${{ secrets.PAYMENT_SERVICE_SECRET }}/' \
              -e 's/^paymentShoppingKey=.*/paymentShoppingKey=${{ secrets.PAYMENT_SHOPPING_KEY }}/' \
              -e 's/^paymentTopUpKey=.*/paymentTopUpKey=${{ secrets.PAYMENT_TOPUP_KEY }}/' \
              -e 's/^paymentBillingKey=.*/paymentBillingKey=${{ secrets.PAYMENT_BILLING_KEY }}/' \
              -e 's/^paymentGratuityKey=.*/paymentGratuityKey=${{ secrets.PAYMENT_GRATUITY_KEY }}/' \
              -e "s/^mysqlUserPassword=.*/mysqlUserPassword=${{ secrets.MYSQL_USER_PASSWORD }}/" \
              -e "s/^sshPassphrase=.*/sshPassphrase=${{ secrets.SSH_KEY_PASSPHRASE }}/" \
              -e "s/^mysqlHost=.*/mysqlHost=${{ vars.MYSQL_HOST }}/" \
              -e "s/^mysqlUserName=.*/mysqlUserName=${{ vars.MYSQL_USERNAME }}/" \
              -e "s/^mysqlDatabaseName=.*/mysqlDatabaseName=${{ vars.MYSQL_DATABASE_NAME }}/" \
              -e "s/^mysqlServerPort=.*/mysqlServerPort=${{ vars.MYSQL_SERVER_PORT }}/" \
              -e "s/^sshConnectionRequired=.*/sshConnectionRequired=${{ vars.SSH_CONNECTION_REQUIRED }}/" \
              -e "s/^sshHost=.*/sshHost=${{ vars.SSH_HOST }}/" \
              -e "s/^sshUserName=.*/sshUserName=${{ vars.SSH_USERNAME }}/" \
              -e "s/^sshPort=.*/sshPort=${{ vars.SSH_PORT }}/" \
              -e "s/^isSshKeyProtected=.*/isSshKeyProtected=${{ vars.IS_SSH_KEY_PROTECTED }}/" \
              -e "s|^sshKeyPath=.*|sshKeyPath=${{ vars.SSH_KEY_PATH }}|" \
              -e "s|^baseURL=.*|baseURL=${{ vars.BASE_URL }}|" \
              -e "s|^controlRoomBaseURL=.*|controlRoomBaseURL=${{ vars.CONTORL_ROOM_BASE_URL }}|" \
              -e "s|^billingServicesBaseURL=.*|billingServicesBaseURL=${{ vars.BILLING_SERVICE_BASE_URL }}|" \
              -e "s|^midMileBaseURL=.*|midMileBaseURL=${{ vars.MID_MILE_BASE_URL }}|" \
              -e "s|^transitBaseURL=.*|transitBaseURL=${{ vars.TRANSIT_BASE_URL }}|" \
              -e "s|^cardServicesBaseURL=.*|cardServicesBaseURL=${{ vars.CARD_SERVICES_BASE_URL }}|" \
              -e "s|^cardServicesAdminPanelBaseURL=.*|cardServicesAdminPanelBaseURL=${{ vars.CARD_SERVICES_ADMIN_PANEL_BASE_URL }}|" \
              -e "s|^pickerServicesBaseURL=.*|pickerServicesBaseURL=${{ vars.PICKER_SERVICES_BASE_URL }}|" \
              -e "s|^logisticsBaseURL=.*|logisticsBaseURL=${{ vars.LOGISTICS_BASE_URL }}|" \
              -e "s|^fleetAppBaseURL=.*|fleetAppBaseURL=${{ vars.FLEET_APP_BASE_URL }}|" \
              -e "s|^fleetServiceBaseURL=.*|fleetServiceBaseURL=${{ vars.FLEET_SERVICE_BASE_URL }}|" \
              -e "s|^paymentServiceBaseURL=.*|paymentServiceBaseURL=${{ vars.PAYMENT_SERVICE_BASE_URL }}|" \
              -e "s|^internalOrdersBaseURL=.*|internalOrdersBaseURL=${{ vars.INTERNAL_ORDERS_BASE_URL }}|" \
              -e "s|^foodAggregatorServiceBaseURL=.*|foodAggregatorServiceBaseURL=${{ vars.FOOD_AGGREGATOR_BASE_URL }}|" \
              -e "s|^catalogBaseURL=.*|catalogBaseURL=${{ vars.CATALOG_BASE_URL }}|" \
              -e "s|^inaiBaseURL=.*|inaiBaseURL=${{ vars.INAI_BASE_URL }}|" \
              -e "s|^stockTakeBaseURL=.*|stockTakeBaseURL=${{ vars.STOCK_TAKE_BASE_URL }}|" \
              -e "s|^freshChatApiBaseURL=.*|freshChatApiBaseURL=${{ vars.FRESH_CHAT_API_BASE_URL }}|" \
              -e "s|^orderServiceBaseURL=.*|orderServiceBaseURL=${{ vars.ORDER_SERVICE_BASE_URL }}|" \
              -e "s|^dAPhoneNumber=.*|dAPhoneNumber=${{ vars.DA_PHONE_NUMBER }}|" \
              -e "s|^dAPhoneCountryCode=.*|dAPhoneCountryCode=${{ vars.DA_PHONE_COUNTRY_CODE }}|" \
              -e "s|^dAPassword=.*|dAPassword=${{ vars.DA_PASSWORD }}|" \
              -e "s|^fpManagerPhoneNumber=.*|fpManagerPhoneNumber=${{ vars.FP_MANAGER_PHONE_NUMBER }}|" \
              -e "s|^fpManagerPhoneCountryCode=.*|fpManagerPhoneCountryCode=${{ vars.FP_MANAGER_COUNTRY_CODE }}|" \
              -e "s|^fpManagerPassword=.*|fpManagerPassword=${{ vars.FP_MANAGER_PASSWORD }}|" \
              -e "s|^paymentPanelEmail=.*|paymentPanelEmail=${{ vars.PAYMENT_PANEL_EMAIL }}|" \
              -e "s|^paymentPanelPassword=.*|paymentPanelPassword=${{ secrets.PAYMENT_PANEL_PASSWORD }}|" \
              -e "s|^freshChatApiKey=.*|freshChatApiKey=${{ secrets.FRESH_CHAT_API_KEY }}|" \
              -e "s|^supplyChainStaticAuthToken=.*|supplyChainStaticAuthToken=${{ secrets.SUPPLY_CHAIN_STATIC_AUTH_TOKEN }}|" \
              -e "s|^freshChatChannelId=.*|freshChatChannelId=${{ vars.FRESH_CHAT_CHANNEL_ID }}|" \
              -e "s|^fallBackProductIds=.*|fallBackProductIds=${{ vars.FALLBACK_PRODUCT_IDS }}|" \
              -e "s|^targetQtyForEachFallBackProduct=.*|targetQtyForEachFallBackProduct=${{ vars.TARGET_QTY_FOR_EACH_FALLBACK_PRODUCT }}|" \
              -e "s|^excludedCategoryIds=.*|excludedCategoryIds=${{ vars.EXCLUDED_CATEGORY_IDS }}|" \
          resources/environments/config_${{ env.SELECTED_ENV }}.properties

      - name: Display modified config file contents
        run: |
          echo "Contents of config.properties:"
          cat resources/environments/config_${{ env.SELECTED_ENV }}.properties

      - name: Modify cardServiceConfigs.properties
        run: |
          sed -i'' \
            -e "s|^adminUserName=.*|adminUserName=${{ vars.TESTING_CARDSERVICE_ADMIN_USERNAME }}|" \
            -e "s|^adminPassword=.*|adminPassword=${{ secrets.TESTING_CARDSERVICE_ADMIN_PASSWORD }}|" \
            -e "s|^loginMobileSchemeUserName=.*|loginMobileSchemeUserName=${{ vars.TESTING_CARDSERVICE_LOGIN_MOBILE_SCHEME_USER_NAME }}|" \
            -e "s|^loginMobileSchemePassword=.*|loginMobileSchemePassword=${{ secrets.TESTING_CARDSERVICE_LOGIN_MOBILE_SCHEME_PASSWORD }}|" \
            -e "s|^defaultCardPasscode=.*|defaultCardPasscode=${{ secrets.CARDSERVICE_DEFAULT_PASSCODE }}|" \
            -e "s|^cardUserBackTransactionId=.*|cardUserBackTransactionId=${{ vars.CARDSERVICE_CARD_USER_ID_BACK_TRANSACTION_ID }}|" \
            -e "s|^cardUserFrontTransactionId=.*|cardUserFrontTransactionId=${{ vars.CARDSERVICE_CARD_USER_ID_FRONT_TRANSACTION_ID }}|" \
            -e "s|^cardServiceContractNumber=.*|cardServiceContractNumber=${{ vars.CARDSERVICE_CONTRACT_NUMBER }}|" \
            -e "s|^cardServiceProductNumber=.*|cardServiceProductNumber=${{ vars.CARDSERVICE_PRODUCT_NUMBER }}|" \
            -e "s|^cardServiceTypeId=.*|cardServiceTypeId=${{ vars.CARDSERVICE_TYPE_ID }}|" \
          resources/environments/cardServiceConfigs_${{ env.SELECTED_ENV }}.properties

      - name: Modify cardServiceEncryptionPublicKey.pub contents
        run: |
          echo "${{ secrets.TESTING_CARDSERVICE_MOBILE_USER_PUBLIC_KEY }}" > resources/environments/cardServiceEncryptionPublicKey.pub

      - name: Modify dbSshKey contents
        run: |
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > resources/environments/dbSshKey

      - name: Display modified card service config file contents
        run: |
          echo "Contents of cardServiceConfigs.properties:"
          cat resources/environments/cardServiceConfigs_${{ env.SELECTED_ENV }}.properties

#      - name: Setup Android SDK
#        uses: android-actions/setup-android@v3
#
#      - name: Set Android related PATH changes Dynamically
#        run: |
#          CMDLINE_TOOLS_DIR=$(ls -d $ANDROID_HOME/cmdline-tools/* | head -n 1)
#          echo "CMDLINE_TOOLS_DIR=$CMDLINE_TOOLS_DIR" >> $GITHUB_ENV
#          echo "Current cmdline-tools path is: $CMDLINE_TOOLS_DIR"
#          echo "$ANDROID_HOME/"
#          echo "$HOME/.config/.android/avd" >> $GITHUB_PATH
#          echo "$CMDLINE_TOOLS_DIR/bin" >> $GITHUB_PATH
#          echo "$ANDROID_HOME/platform-tools" >> $GITHUB_PATH
#          echo "$ANDROID_HOME/emulator" >> $GITHUB_PATH
#
#      - name: Create AVD
#        run: |
#          echo "y" | $CMDLINE_TOOLS_DIR/bin/sdkmanager "system-images;android-31;google_apis;x86_64"
#          echo "no" | $CMDLINE_TOOLS_DIR/bin/avdmanager create avd -n Pixel_2 -k "system-images;android-31;google_apis;x86_64" -d "pixel_2_xl"
#          echo "ANDROID_AVD_HOME=$HOME/.config/.android/avd" >> $GITHUB_ENV
#          $ANDROID_HOME/emulator/emulator -list-avds
#          $ANDROID_HOME/platform-tools/adb devices
#
#      - name: Print some logs for the Android PATHs setup
#        run: |
#          echo "Files in HOME directory:"
#          ls -la $HOME
#          echo "Files in HOME/.android"
#          ls -la $HOME/.android
#          echo "Current PATH:"
#          echo "$PATH"
#          echo "Current Android HOME contents:"
#          ls -la $ANDROID_HOME
#          echo "Current Android folder contents:"
#          ls -la $HOME/.android
#          echo "Current CDMLINE-TOOLS: ${{ env.CMDLINE_TOOLS_DIR }}"
#          echo "Current ANDROID SDK ROOT is: ${{ env.ANDROID_SDK_ROOT }}"
#          echo "Current ANDROID AVD HOME is: ${{ env.ANDROID_AVD_HOME }}"
#          echo "Available AVDs for avdManager:"
#          $CMDLINE_TOOLS_DIR/bin/avdmanager list avd
#
#      - name: Install Appium
#        run: npm i --location=global appium
#
#      - name: Install Appium drivers
#        run: |
#          appium driver install uiautomator2
#          appium driver install xcuitest

      - name: Install Google Chrome
        run: |
          wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
          echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
          sudo apt-get update
          sudo apt-get install -y google-chrome-stable

      - name: Install ffmpeg
        run: |
          sudo apt-get update
          sudo apt-get install -y ffmpeg

      - name: Build with Maven and Execute Tests
        run: mvn test -Pdefault-tests -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }}

      - name: Generate Allure Reports
        if: ${{ always() }}
        run: mvn allure:report

      - name: Archive Allure Reports
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: allure-reports
          path: target/site/allure-maven-plugin

      - name: Upload screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: screenshots
          path: resources/screenshots/**
          if-no-files-found: ignore
          retention-days: 14

#      - name: Publish Allure Report to GitHub Pages
#        uses: peaceiris/actions-gh-pages@v4
#        with:
#          github_token: ${{ secrets.GITHUB_TOKEN }}
#          publish_dir: ./target/site/allure-maven-plugin

      - name: Archive TestNG Reports
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: testng-reports
          path: target/surefire-reports

      - name: Comment with Allure Report Link
        if: ${{ always() && github.event_name == 'pull_request' }}
        uses: actions/github-script@v5
        with:
          script: |
            const reportUrl = `https://${{ github.repository_owner }}.github.io/${{ github.repository }}/`;
            const message = `📊 **Allure Report Published:** [View Report](${reportUrl})`;
            const prNumber = context.issue.number; // Directly use the pull request number from the event context
            console.log(`Pull Request Number: ${prNumber}`); // Log the pull request number
            github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: prNumber,
              body: message
            });
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Check Test Results
        if: ${{ always() }}
        id: check_test_results
        run: |
          echo "status=$(grep -E 'Tests run: [0-9]+, Failures: [1-9]+' target/surefire-reports/*.txt | wc -l)" >> $GITHUB_OUTPUT
