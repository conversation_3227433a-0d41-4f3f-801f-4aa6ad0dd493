name: Food Aggregator Test

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select the target environment'
        required: true
        default: 'testing'
        type: choice
        options:
          - testing
          - integration
      test_country_code:
        description: 'Select the test country code'
        required: true
        default: 'EG'
        type: choice
        options:
          - EG
          - KSA
      use_order_api_v2:
        description: 'Flag to use Order API V2'
        required: true
        default: true
        type: boolean
      target_app_platform:
        description: 'Select the target app platform'
        required: true
        default: 'android'
        type: choice
        options:
          - android
          - ios
      target_app_name:
        description: 'Select the target app to test'
        required: true
        default: 'customerAppNative'
        type: choice
        options:
          - customerAppNative
          - customerAppReactNative
          - midMileApp
          - fleetApp
      target_test_name:
        description: 'Select the target test name'
        required: true
        default: 'Food Aggregator Tests'
        type: choice
        options:
          - Food Aggregator Tests
      target_test_group:
        description: 'Type a group name for the tests. If you want the full tests in the test tag to run, leave empty'
        required: false
        default: ''
      target_app_id:
        description: 'Enter the target-App ID from browserStack or leave as is if you want to use stored value'
        required: false
        type: string
        default: 'bs://'
      target_app_build_number:
        description: 'Enter the target-App build version and number leave empty if no app ID is provided. Accepted values are digits, dot, () and hyphen'
        required: false
        type: string
        default: ''
      jira_ticket_id:
        description: 'Enter the Jira Ticket ID (digits, B, and - only)'
        required: true
        type: string
        default: 'B10-'
  repository_dispatch:
    types: [ food-aggregator-test ]

jobs:
  sanity-test-check:
    name: Food Aggregator Test - ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }} - ${{ github.event.client_payload.target_app_platform || github.event.inputs.target_app_platform || 'android' }} - ${{ github.event.client_payload.target_app_name || github.event.inputs.target_app_name || 'customerAppNative' }} - Order API V2 (${{ github.event.client_payload.use_order_api_v2 || github.event.inputs.use_order_api_v2 || 'true' }}) - Groups(${{ github.event.client_payload.target_test_group || github.event.inputs.target_test_group || 'undefined' }}) - Jira Ticket ID (${{ github.event.client_payload.jira_ticket_id || github.event.inputs.jira_ticket_id || 'B10-' }})
    runs-on: ubuntu-latest
    timeout-minutes: 60
    environment: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }}
    env:
      SELECTED_ENV: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }}
      USE_ORDER_API_V2: ${{ github.event.client_payload.use_order_api_v2 || github.event.inputs.use_order_api_v2 || 'true' }}
      TEST_COUNTRY_CODE: ${{ github.event.client_payload.test_country_code || github.event.inputs.test_country_code || 'EG' }}
      TRIGGERING_REPO_OWNER: ${{ github.event.client_payload.triggering_repo_owner || 'undefined' }}
      TRIGGERING_REPO: ${{ github.event.client_payload.triggering_repo || 'undefined' }}
      TRIGGERING_SHA_ID: ${{ github.event.client_payload.sha_id || 'undefined' }}
      JIRA_TICKET_ID: ${{ github.event.client_payload.jira_ticket_id || github.event.inputs.jira_ticket_id || 'B10-' }}
      TARGET_APP_PLATFORM: ${{ github.event.client_payload.target_app_platform || github.event.inputs.target_app_platform || 'android' }}
      TARGET_APP_NAME: ${{ github.event.client_payload.target_app_name || github.event.inputs.target_app_name || 'customerAppNative' }}
      TARGET_APP_ID: ${{ github.event.client_payload.target_app_id || github.event.inputs.target_app_id || 'undefined' }}
      TARGET_APP_BUILD_NUMBER: ${{ github.event.client_payload.target_app_build_number || github.event.inputs.target_app_build_number || 'undefined' }}
      TARGET_TEST_NAME: ${{ github.event.client_payload.target_test_name || github.event.inputs.target_test_name || 'Food Aggregator Tests' }}
      TARGET_TEST_GROUP: ${{ github.event.client_payload.target_test_group || github.event.inputs.target_test_group || 'undefined' }}

    steps:
      - name: Validate Inputs and Values
        run: |
          # Validate presence of required fields for repository_dispatch
          if [ "${{ github.event_name }}" == "repository_dispatch" ]; then
            if [ -z "${{ github.event.client_payload.triggering_repo_owner }}" ] || \
               [ -z "${{ github.event.client_payload.triggering_repo }}" ] || \
               [ -z "${{ github.event.client_payload.sha_id }}" ] || \
               [ -z "${{ github.event.client_payload.jira_ticket_id }}" ]; then
              echo "Error: Required fields in client_payload are missing for repository_dispatch!"
              echo "Triggering Repo Owner: '${{ github.event.client_payload.triggering_repo_owner }}'"
              echo "Triggering Repo: '${{ github.event.client_payload.triggering_repo }}'"
              echo "Triggering SHA ID: '${{ github.event.client_payload.sha_id }}'"
              echo "Jira Ticket ID: '${{ github.event.client_payload.jira_ticket_id }}'"
              exit 1
            fi
          fi
          # Validate environment
          if [[ "${{ env.SELECTED_ENV }}" != "testing" && \
                "${{ env.SELECTED_ENV }}" != "integration" ]]; then
            echo "Error: Invalid environment '${{ env.SELECTED_ENV }}'. Must be one of: testing, integration"
            exit 1
          fi
          # Validate use_order_api_v2
          if [[ "${{ env.USE_ORDER_API_V2 }}" != "true" && \
                "${{ env.USE_ORDER_API_V2 }}" != "false" ]]; then
            echo "Error: Invalid use_order_api_v2 '${{ env.USE_ORDER_API_V2 }}'. Must be true or false."
            exit 1
          fi
          # Validate test_country_code
          if [[ "${{ env.TEST_COUNTRY_CODE }}" != "EG" && \
                "${{ env.TEST_COUNTRY_CODE }}" != "KSA" ]]; then
            echo "Error: Invalid test_country_code '${{ env.TEST_COUNTRY_CODE }}'. Must be one of: EG, KSA."
            exit 1
          fi
          # Validate JIRA_TICKET_ID format
          if [[ ! "${{ env.JIRA_TICKET_ID }}" =~ ^B10-[0-9]+$ ]]; then
            echo "Error: Invalid JIRA_TICKET_ID format. Must be 'B10-<digits>'."
            exit 1
          fi
          # Validate TARGET_APP_PLATFORM
          if [[ "${{ env.TARGET_APP_PLATFORM }}" != "android" && \
                "${{ env.TARGET_APP_PLATFORM }}" != "ios" ]]; then
            echo "Error: Invalid target_app_platform '${{ env.TARGET_APP_PLATFORM }}'. Must be one of: android, ios"
            exit 1
          fi
          # Validate TARGET_APP_NAME
          if [[ "${{ env.TARGET_APP_NAME }}" != "customerAppNative" && \
                "${{ env.TARGET_APP_NAME }}" != "customerAppReactNative" && \
                "${{ env.TARGET_APP_NAME }}" != "midMileApp" && \
                "${{ env.TARGET_APP_NAME }}" != "fleetApp" ]]; then
            echo "Error: Invalid target_app_name '${{ env.TARGET_APP_NAME }}'. Must be one of: customerAppNative, customerAppReactNative, midMileApp, fleetApp"
            exit 1
          fi
          # Validate TARGET_TEST_NAME
          if [[ "${{ env.TARGET_TEST_NAME }}" != "Food Aggregator Tests" ]]; then
            echo "Error: Invalid target_test_name '${{ env.TARGET_TEST_NAME }}'. Must be: Food Aggregator Tests"
            exit 1
          fi
          # Validate TARGET_APP_ID
          if [[ -n "${{ env.TARGET_APP_ID }}" && "${{ env.TARGET_APP_ID }}" != "bs://" && ! "${{ env.TARGET_APP_ID }}" =~ ^bs://[a-zA-Z0-9]{10,}$ ]]; then
            echo "Error: Invalid target_app_id value. Must start with 'bs://' and be followed by at least 10 alphanumeric characters."
            exit 1
          fi
          # Validate Target app build number
          if [[ -n "${{ env.TARGET_APP_ID }}" && "${{ env.TARGET_APP_ID }}" != "bs://" ]]; then
            if [[ -z "${{ env.TARGET_APP_BUILD_NUMBER }}" || "${{ env.TARGET_APP_BUILD_NUMBER }}" == "undefined" ]]; then
              echo "Error: target_app_build_number is required when target_app_id is provided and is not 'bs://'."
              exit 1
            fi
            if [[ ! "${{ env.TARGET_APP_BUILD_NUMBER }}" =~ ^[0-9.\(\)\-]+$ ]]; then
              echo "Error: Invalid target_app_build_number. Only digits, dots, parentheses and hyphen are allowed."
              exit 1
            fi
          fi
          echo "All validations passed successfully!"
        shell: bash

      - name: Create Pending Status Check
        if: github.event_name == 'repository_dispatch'
        run: |
          STATE="pending"
          DESCRIPTION="Food Aggregator Test is in progress."
          TARGET_URL="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -s -X POST \
            -H "Authorization: Bearer ${{ secrets.GH_PAT }}" \
            -H "Accept: application/vnd.github+json" \
            -d '{
              "state": "'"$STATE"'",
              "target_url": "'"$TARGET_URL"'",
              "description": "'"$DESCRIPTION"'",
              "context": "Food Aggregator Tests Result"
            }' \
            https://api.github.com/repos/${{ env.TRIGGERING_REPO_OWNER }}/${{ env.TRIGGERING_REPO }}/statuses/${{ env.TRIGGERING_SHA_ID }}

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 24
        uses: actions/setup-java@v4
        with:
          java-version: '24'
          distribution: 'oracle'
          cache: maven

      - name: Set up Maven
        uses: stCarolas/setup-maven@v5
        with:
          maven-version: 3.9.8

      - name: Install Node.js and npm
        uses: actions/setup-node@v4
        with:
          node-version: '20.18.0'
          registry-url: 'https://registry.npmjs.org/'

      - name: Copy config files
        run: |
          cp resources/environments/config.properties.example resources/environments/config_${{ env.SELECTED_ENV }}.properties
          cp resources/environments/webConfig.properties.example resources/environments/webConfig.properties
          cp resources/environments/cardServiceConfigs.properties.example resources/environments/cardServiceConfigs_${{ env.SELECTED_ENV }}.properties
          cp resources/environments/browserStackConfigs.properties.example resources/environments/browserStackConfigs.properties
          cp resources/environments/iPhone_14.properties.example resources/environments/iPhone_14.properties
          cp resources/environments/Pixel_2.properties.example resources/environments/Pixel_2.properties
          cp resources/environments/cardServiceEncryptionPublicKey.pub.example resources/environments/cardServiceEncryptionPublicKey.pub
          cp resources/environments/dbSshKey.example resources/environments/dbSshKey

      - name: Set Test Mobile Number
        id: set_mobile_number
        run: |
          if [ "$TEST_COUNTRY_CODE" == "EG" ]; then
            echo "test_mobile_number=+20" >> $GITHUB_ENV
          else
            echo "test_mobile_number=+966" >> $GITHUB_ENV
          fi

      - name: Select Test Mobile Company
        id: select_mobile_company
        run: |
          if [ "$TEST_COUNTRY_CODE" == "EG" ]; then
            echo "mobile_company=Etisalat" >> $GITHUB_ENV
          else
            echo "mobile_company=Zain" >> $GITHUB_ENV
          fi

      - name: Fetch Latest Stable Chrome Version
        run: |
          LATEST_STABLE_CHROME_VERSION=$(curl -s https://googlechromelabs.github.io/chrome-for-testing/LATEST_RELEASE_STABLE)
          echo "LATEST_STABLE_CHROME_VERSION=$LATEST_STABLE_CHROME_VERSION" >> $GITHUB_ENV

      - name: Download ChromeDriver Matching Latest Stable Chrome
        run: |
          CHROMEDRIVER_URL="https://storage.googleapis.com/chrome-for-testing-public/${{ env.LATEST_STABLE_CHROME_VERSION }}/linux64/chromedriver-linux64.zip"
          curl -s -o chromedriver.zip "$CHROMEDRIVER_URL"
          unzip -q chromedriver.zip -d resources/webDrivers
          mv resources/webDrivers/chromedriver-linux64/chromedriver resources/webDrivers/
          chmod +x resources/webDrivers/chromedriver
          ls -la resources/webDrivers/

      - name: Install Google Chrome
        run: |
          wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
          echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
          sudo apt-get update
          sudo apt-get install -y google-chrome-stable

      - name: Install OpenSSH client
        run: |
          sudo apt-get update
          sudo apt-get install -y openssh-client

      - name: Install MySQL client
        run: |
          sudo apt-get update
          sudo apt-get install -y mysql-client

      - name: Modify config.properties file
        run: |
          sed -i'' \
              -e "s/^testCountryCode=.*/testCountryCode=$TEST_COUNTRY_CODE/" \
              -e "s/^testMobileNumber=.*/testMobileNumber=${{ env.test_mobile_number }}/" \
              -e "s/^testMobileCompany=.*/testMobileCompany=${{ env.mobile_company }}/" \
              -e "s/^testFpName=.*/testFpName=${{ vars.FP_NAME }}/" \
              -e "s/^testFpDate=.*/testFpDate=${{ vars.FP_DATE }}/" \
              -e "s/^testLatitude=.*/testLatitude=${{ vars.WAREHOUSE_LATITUDE }}/" \
              -e "s/^testLongitude=.*/testLongitude=${{ vars.WAREHOUSE_LONGITUDE }}/" \
              -e "s|^testOrderInfo=.*|testOrderInfo=${{ vars.ORDER_INFO }}|" \
              -e "s/^adminBypassScriptPassword=.*/adminBypassScriptPassword=${{ secrets.ADMIN_BYPASS_SCRIPT_PASSWORD }}/" \
              -e "s/^adminGmailAddress=.*/adminGmailAddress=${{ secrets.ADMIN_GMAIL_ADDRESS }}/" \
              -e "s/^adminGmailPassword=.*/adminGmailPassword=${{ secrets.ADMIN_GMAIL_PASSWORD }}/" \
              -e "s/^midMilePassword=.*/midMilePassword=${{ secrets.MID_MILE_PASSWORD }}/" \
              -e "s/^ngrokAuthToken=.*/ngrokAuthToken=${{ secrets.NGROK_AUTH_TOKEN }}/" \
              -e "s/^pickerPassword=.*/pickerPassword=${{ secrets.PICKER_PASSWORD }}/" \
              -e "s/^slackApiToken=.*/slackApiToken=${{ secrets.SLACK_API_TOKEN }}/" \
              -e "s/^wpLoggedInCookieName=.*/wpLoggedInCookieName=${{ secrets.WP_LOGGED_IN_COOKIE_NAME }}/" \
              -e "s/^wpNodeAuthorizationCookieName=.*/wpNodeAuthorizationCookieName=${{ secrets.WP_NODE_AUTHORIZATION_COOKIE_NAME }}/" \
              -e "s/^wpSecCookieName=.*/wpSecCookieName=${{ secrets.WP_SEC_COOKIE_NAME }}/" \
              -e "s/^adminLocalPhoneNumber=.*/adminLocalPhoneNumber=${{ vars.ADMIN_LOCAL_PHONE_NUMBER }}/" \
              -e "s/^adminReferralCode=.*/adminReferralCode=${{ vars.ADMIN_REFERRAL_CODE }}/" \
              -e "s/^midMilePhoneNumber=.*/midMilePhoneNumber=${{ vars.MID_MILE_PHONE_NUMBER }}/" \
              -e "s/^pickerPhoneNumber=.*/pickerPhoneNumber=${{ vars.PICKER_PHONE_NUMBER }}/" \
              -e 's/^paymentServiceSecret=.*/paymentServiceSecret=${{ secrets.PAYMENT_SERVICE_SECRET }}/' \
              -e 's/^paymentShoppingKey=.*/paymentShoppingKey=${{ secrets.PAYMENT_SHOPPING_KEY }}/' \
              -e 's/^paymentTopUpKey=.*/paymentTopUpKey=${{ secrets.PAYMENT_TOPUP_KEY }}/' \
              -e 's/^paymentBillingKey=.*/paymentBillingKey=${{ secrets.PAYMENT_BILLING_KEY }}/' \
              -e 's/^paymentGratuityKey=.*/paymentGratuityKey=${{ secrets.PAYMENT_GRATUITY_KEY }}/' \
              -e "s/^mysqlUserPassword=.*/mysqlUserPassword=${{ secrets.MYSQL_USER_PASSWORD }}/" \
              -e "s/^sshPassphrase=.*/sshPassphrase=${{ secrets.SSH_KEY_PASSPHRASE }}/" \
              -e "s/^mysqlHost=.*/mysqlHost=${{ vars.MYSQL_HOST }}/" \
              -e "s/^mysqlUserName=.*/mysqlUserName=${{ vars.MYSQL_USERNAME }}/" \
              -e "s/^mysqlDatabaseName=.*/mysqlDatabaseName=${{ vars.MYSQL_DATABASE_NAME }}/" \
              -e "s/^mysqlServerPort=.*/mysqlServerPort=${{ vars.MYSQL_SERVER_PORT }}/" \
              -e "s/^sshConnectionRequired=.*/sshConnectionRequired=${{ vars.SSH_CONNECTION_REQUIRED }}/" \
              -e "s/^sshHost=.*/sshHost=${{ vars.SSH_HOST }}/" \
              -e "s/^sshUserName=.*/sshUserName=${{ vars.SSH_USERNAME }}/" \
              -e "s/^sshPort=.*/sshPort=${{ vars.SSH_PORT }}/" \
              -e "s/^isSshKeyProtected=.*/isSshKeyProtected=${{ vars.IS_SSH_KEY_PROTECTED }}/" \
              -e "s|^sshKeyPath=.*|sshKeyPath=${{ vars.SSH_KEY_PATH }}|" \
              -e "s|^baseURL=.*|baseURL=${{ vars.BASE_URL }}|" \
              -e "s|^controlRoomBaseURL=.*|controlRoomBaseURL=${{ vars.CONTORL_ROOM_BASE_URL }}|" \
              -e "s|^billingServicesBaseURL=.*|billingServicesBaseURL=${{ vars.BILLING_SERVICE_BASE_URL }}|" \
              -e "s|^midMileBaseURL=.*|midMileBaseURL=${{ vars.MID_MILE_BASE_URL }}|" \
              -e "s|^transitBaseURL=.*|transitBaseURL=${{ vars.TRANSIT_BASE_URL }}|" \
              -e "s|^cardServicesBaseURL=.*|cardServicesBaseURL=${{ vars.CARD_SERVICES_BASE_URL }}|" \
              -e "s|^cardServicesAdminPanelBaseURL=.*|cardServicesAdminPanelBaseURL=${{ vars.CARD_SERVICES_ADMIN_PANEL_BASE_URL }}|" \
              -e "s|^pickerServicesBaseURL=.*|pickerServicesBaseURL=${{ vars.PICKER_SERVICES_BASE_URL }}|" \
              -e "s|^logisticsBaseURL=.*|logisticsBaseURL=${{ vars.LOGISTICS_BASE_URL }}|" \
              -e "s|^fleetAppBaseURL=.*|fleetAppBaseURL=${{ vars.FLEET_APP_BASE_URL }}|" \
              -e "s|^fleetServiceBaseURL=.*|fleetServiceBaseURL=${{ vars.FLEET_SERVICE_BASE_URL }}|" \
              -e "s|^paymentServiceBaseURL=.*|paymentServiceBaseURL=${{ vars.PAYMENT_SERVICE_BASE_URL }}|" \
              -e "s|^internalOrdersBaseURL=.*|internalOrdersBaseURL=${{ vars.INTERNAL_ORDERS_BASE_URL }}|" \
              -e "s|^foodAggregatorServiceBaseURL=.*|foodAggregatorServiceBaseURL=${{ vars.FOOD_AGGREGATOR_BASE_URL }}|" \
              -e "s|^catalogBaseURL=.*|catalogBaseURL=${{ vars.CATALOG_BASE_URL }}|" \
              -e "s|^inaiBaseURL=.*|inaiBaseURL=${{ vars.INAI_BASE_URL }}|" \
              -e "s|^stockTakeBaseURL=.*|stockTakeBaseURL=${{ vars.STOCK_TAKE_BASE_URL }}|" \
              -e "s|^freshChatApiBaseURL=.*|freshChatApiBaseURL=${{ vars.FRESH_CHAT_API_BASE_URL }}|" \
              -e "s|^orderServiceBaseURL=.*|orderServiceBaseURL=${{ vars.ORDER_SERVICE_BASE_URL }}|" \
              -e "s|^dAPhoneNumber=.*|dAPhoneNumber=${{ vars.DA_PHONE_NUMBER }}|" \
              -e "s|^dAPhoneCountryCode=.*|dAPhoneCountryCode=${{ vars.DA_PHONE_COUNTRY_CODE }}|" \
              -e "s|^dAPassword=.*|dAPassword=${{ vars.DA_PASSWORD }}|" \
              -e "s|^fpManagerPhoneNumber=.*|fpManagerPhoneNumber=${{ vars.FP_MANAGER_PHONE_NUMBER }}|" \
              -e "s|^fpManagerPhoneCountryCode=.*|fpManagerPhoneCountryCode=${{ vars.FP_MANAGER_COUNTRY_CODE }}|" \
              -e "s|^fpManagerPassword=.*|fpManagerPassword=${{ vars.FP_MANAGER_PASSWORD }}|" \
              -e "s|^paymentPanelEmail=.*|paymentPanelEmail=${{ vars.PAYMENT_PANEL_EMAIL }}|" \
              -e "s|^paymentPanelPassword=.*|paymentPanelPassword=${{ secrets.PAYMENT_PANEL_PASSWORD }}|" \
              -e "s|^freshChatApiKey=.*|freshChatApiKey=${{ secrets.FRESH_CHAT_API_KEY }}|" \
              -e "s|^supplyChainStaticAuthToken=.*|supplyChainStaticAuthToken=${{ secrets.SUPPLY_CHAIN_STATIC_AUTH_TOKEN }}|" \
              -e "s|^freshChatChannelId=.*|freshChatChannelId=${{ vars.FRESH_CHAT_CHANNEL_ID }}|" \
              -e "s|^fallBackProductIds=.*|fallBackProductIds=${{ vars.FALLBACK_PRODUCT_IDS }}|" \
              -e "s|^targetQtyForEachFallBackProduct=.*|targetQtyForEachFallBackProduct=${{ vars.TARGET_QTY_FOR_EACH_FALLBACK_PRODUCT }}|" \
              -e "s|^excludedCategoryIds=.*|excludedCategoryIds=${{ vars.EXCLUDED_CATEGORY_IDS }}|" \
              -e "s|^yeloBaseURL=.*|yeloBaseURL=${{ vars.YELOBASEURL }}|" \
          resources/environments/config_${{ env.SELECTED_ENV }}.properties

      - name: Display modified config file contents
        run: |
          echo "Contents of config.properties:"
          cat resources/environments/config_${{ env.SELECTED_ENV }}.properties

      - name: Modify cardServiceConfigs.properties
        run: |
          sed -i'' \
            -e "s|^adminUserName=.*|adminUserName=${{ vars.TESTING_CARDSERVICE_ADMIN_USERNAME }}|" \
            -e "s|^adminPassword=.*|adminPassword=${{ secrets.TESTING_CARDSERVICE_ADMIN_PASSWORD }}|" \
            -e "s|^loginMobileSchemeUserName=.*|loginMobileSchemeUserName=${{ vars.TESTING_CARDSERVICE_LOGIN_MOBILE_SCHEME_USER_NAME }}|" \
            -e "s|^loginMobileSchemePassword=.*|loginMobileSchemePassword=${{ secrets.TESTING_CARDSERVICE_LOGIN_MOBILE_SCHEME_PASSWORD }}|" \
            -e "s|^defaultCardPasscode=.*|defaultCardPasscode=${{ secrets.CARDSERVICE_DEFAULT_PASSCODE }}|" \
            -e "s|^cardUserBackTransactionId=.*|cardUserBackTransactionId=${{ vars.CARDSERVICE_CARD_USER_ID_BACK_TRANSACTION_ID }}|" \
            -e "s|^cardUserFrontTransactionId=.*|cardUserFrontTransactionId=${{ vars.CARDSERVICE_CARD_USER_ID_FRONT_TRANSACTION_ID }}|" \
            -e "s|^cardServiceContractNumber=.*|cardServiceContractNumber=${{ vars.CARDSERVICE_CONTRACT_NUMBER }}|" \
            -e "s|^cardServiceProductNumber=.*|cardServiceProductNumber=${{ vars.CARDSERVICE_PRODUCT_NUMBER }}|" \
            -e "s|^cardServiceTypeId=.*|cardServiceTypeId=${{ vars.CARDSERVICE_TYPE_ID }}|" \
          resources/environments/cardServiceConfigs_${{ env.SELECTED_ENV }}.properties

      - name: Modify cardServiceEncryptionPublicKey.pub contents
        run: |
          echo "${{ secrets.TESTING_CARDSERVICE_MOBILE_USER_PUBLIC_KEY }}" > resources/environments/cardServiceEncryptionPublicKey.pub

      - name: Modify browserStackConfigs.properties
        run: |
          sed -i'' \
            -e "s|^userName=.*|userName=${{ vars.BROWSERSTACK_USERNAME }}|" \
            -e "s|^accessKey=.*|accessKey=${{ secrets.BROWSERSTACK_ACCESS_KEY }}|" \
            -e "s|^debug=.*|debug=${{ vars.BROWSERSTACK_DEBUG_FLAG }}|" \
            -e "s|^networkLogs=.*|networkLogs=${{ vars.BROWSERSTACK_NETWORKLOGS_FLAG }}|" \
            -e "s|^appiumVersionForAndroidTests=.*|appiumVersionForAndroidTests=${{ vars.BROWSERSTACK_APPIUM_ANDROID_TESTS_VERSION }}|" \
            -e "s|^appiumVersionForIosTests=.*|appiumVersionForIosTests=${{ vars.BROWSERSTACK_APPIUM_IOS_TESTS_VERSION }}|" \
            -e "s|^bStackAndroidCustomerAppNativeApp=.*|bStackAndroidCustomerAppNativeApp=${{ vars.BROWSERSTACK_ANDROID_CUSTOMERAPP_NATIVE_APP }}|" \
            -e "s|^bStackAndroidCustomerAppNativeAppBuildNumber=.*|bStackAndroidCustomerAppNativeAppBuildNumber=${{ vars.BROWSERSTACK_ANDROID_CUSTOMERAPP_NATIVE_APP_BUILD_NUMBER }}|" \
            -e "s|^bStackAndroidCustomerAppReactNativeApp=.*|bStackAndroidCustomerAppReactNativeApp=${{ vars.BROWSERSTACK_ANDROID_CUSTOMERAPP_REACTNATIVE_APP }}|" \
            -e "s|^bStackAndroidCustomerAppReactNativeAppBuildNumber=.*|bStackAndroidCustomerAppReactNativeAppBuildNumber=${{ vars.BROWSERSTACK_ANDROID_CUSTOMERAPP_REACTNATIVE_APP_BUILD_NUMBER }}|" \
            -e "s|^bStackAndroidMidMileApp=.*|bStackAndroidMidMileApp=${{ vars.BROWSERSTACK_ANDROID_MIDMILE_APP }}|" \
            -e "s|^bStackAndroidMidMileAppBuildNumber=.*|bStackAndroidMidMileAppBuildNumber=${{ vars.BROWSERSTACK_ANDROID_MIDMILE_APP_BUILD_NUMBER }}|" \
            -e "s|^bStackAndroidFleetApp=.*|bStackAndroidFleetApp=${{ vars.BROWSERSTACK_ANDROID_FLEET_APP }}|" \
            -e "s|^bStackAndroidFleetAppBuildNumber=.*|bStackAndroidFleetAppBuildNumber=${{ vars.BROWSERSTACK_ANDROID_FLEET_APP_BUILD_NUMBER }}|" \
            -e "s|^androidPlatformVersion=.*|androidPlatformVersion=${{ vars.BROWSERSTACK_ANDROID_PLATFORM_VERSION }}|" \
            -e "s|^androidDeviceName=.*|androidDeviceName=${{ vars.BROWSERSTACK_ANDROID_DEVICE_NAME }}|" \
            -e "s|^androidPlatformVersionDevice2=.*|androidPlatformVersionDevice2=${{ vars.BROWSERSTACK_ANDROID_PLATFORM_VERSION_DEVICE_2 }}|" \
            -e "s|^androidDeviceName2=.*|androidDeviceName2=${{ vars.BROWSERSTACK_ANDROID_DEVICE_NAME_2 }}|" \
            -e "s|^androidPlatformVersionDevice3=.*|androidPlatformVersionDevice3=${{ vars.BROWSERSTACK_ANDROID_PLATFORM_VERSION_DEVICE_3 }}|" \
            -e "s|^androidDeviceName3=.*|androidDeviceName3=${{ vars.BROWSERSTACK_ANDROID_DEVICE_NAME_3 }}|" \
            -e "s|^androidPlatformVersionDevice4=.*|androidPlatformVersionDevice4=${{ vars.BROWSERSTACK_ANDROID_PLATFORM_VERSION_DEVICE_4 }}|" \
            -e "s|^androidDeviceName4=.*|androidDeviceName4=${{ vars.BROWSERSTACK_ANDROID_DEVICE_NAME_4 }}|" \
            -e "s|^bStackIosCustomerAppNativeApp=.*|bStackIosCustomerAppNativeApp=${{ vars.BROWSERSTACK_IOS_CUSTOMERAPP_NATIVE_APP }}|" \
            -e "s|^bStackIosCustomerAppNativeAppBuildNumber=.*|bStackIosCustomerAppNativeAppBuildNumber=${{ vars.BROWSERSTACK_IOS_CUSTOMERAPP_NATIVE_APP_BUILD_NUMBER }}|" \
            -e "s|^bStackIosCustomerAppReactNativeApp=.*|bStackIosCustomerAppReactNativeApp=${{ vars.BROWSERSTACK_IOS_CUSTOMERAPP_REACTNATIVE_APP }}|" \
            -e "s|^bStackIosCustomerAppReactNativeAppBuildNumber=.*|bStackIosCustomerAppReactNativeAppBuildNumber=${{ vars.BROWSERSTACK_IOS_CUSTOMERAPP_REACTNATIVE_APP_BUILD_NUMBER }}|" \
            -e "s|^bStackIosMidMileApp=.*|bStackIosMidMileApp=${{ vars.BROWSERSTACK_IOS_MIDMILE_APP }}|" \
            -e "s|^bStackIosMidMileAppBuildNumber=.*|bStackIosMidMileAppBuildNumber=${{ vars.BROWSERSTACK_IOS_MIDMILE_APP_BUILD_NUMBER }}|" \
            -e "s|^bStackIosFleetApp=.*|bStackIosFleetApp=${{ vars.BROWSERSTACK_IOS_FLEET_APP }}|" \
            -e "s|^bStackIosFleetAppBuildNumber=.*|bStackIosFleetAppBuildNumber=${{ vars.BROWSERSTACK_IOS_FLEET_APP_BUILD_NUMBER }}|" \
            -e "s|^iosPlatformVersion=.*|iosPlatformVersion=${{ vars.BROWSERSTACK_IOS_PLATFORM_VERSION }}|" \
            -e "s|^iosDeviceName=.*|iosDeviceName=${{ vars.BROWSERSTACK_IOS_DEVICE_NAME }}|" \
            -e "s|^iosPlatformVersionDevice2=.*|iosPlatformVersionDevice2=${{ vars.BROWSERSTACK_IOS_PLATFORM_VERSION_DEVICE_2 }}|" \
            -e "s|^iosDeviceName2=.*|iosDeviceName2=${{ vars.BROWSERSTACK_IOS_DEVICE_NAME_2 }}|" \
            -e "s|^iosPlatformVersionDevice3=.*|iosPlatformVersionDevice3=${{ vars.BROWSERSTACK_IOS_PLATFORM_VERSION_DEVICE_3 }}|" \
            -e "s|^iosDeviceName3=.*|iosDeviceName3=${{ vars.BROWSERSTACK_IOS_DEVICE_NAME_3 }}|" \
            -e "s|^iosPlatformVersionDevice4=.*|iosPlatformVersionDevice4=${{ vars.BROWSERSTACK_IOS_PLATFORM_VERSION_DEVICE_4 }}|" \
            -e "s|^iosDeviceName4=.*|iosDeviceName4=${{ vars.BROWSERSTACK_IOS_DEVICE_NAME_4 }}|" \
          resources/environments/browserStackConfigs.properties

      - name: Check and Set TARGET_APP_ID to 'undefined' if it's on default values
        run: |
          if [[ -z "${{ env.TARGET_APP_ID }}" || "${{ env.TARGET_APP_ID }}" == "bs://" ]]; then
            echo "TARGET_APP_ID=" >> $GITHUB_ENV
          fi

      - name: Update BrowserStack App ID for Target App
        if: ${{ env.TARGET_APP_ID != '' && env.TARGET_APP_ID != 'undefined' }}
        run: |
          case "${{ env.TARGET_APP_PLATFORM }}" in
            android)
              case "${{ env.TARGET_APP_NAME }}" in
                customerAppNative)
                  sed -i'' \
                    -e "s|^bStackAndroidCustomerAppNativeApp=.*|bStackAndroidCustomerAppNativeApp=${{ env.TARGET_APP_ID }}|" \
                    -e "s|^bStackAndroidCustomerAppNativeAppBuildNumber=.*|bStackAndroidCustomerAppNativeAppBuildNumber=${{ env.TARGET_APP_BUILD_NUMBER }}|" \
                    resources/environments/browserStackConfigs.properties
                  ;;
                customerAppReactNative)
                  sed -i'' \
                    -e "s|^bStackAndroidCustomerAppReactNativeApp=.*|bStackAndroidCustomerAppReactNativeApp=${{ env.TARGET_APP_ID }}|" \
                    -e "s|^bStackAndroidCustomerAppReactNativeAppBuildNumber=.*|bStackAndroidCustomerAppReactNativeAppBuildNumber=${{ env.TARGET_APP_BUILD_NUMBER }}|" \
                    resources/environments/browserStackConfigs.properties
                  ;;
                midMileApp)
                  sed -i'' \
                    -e "s|^bStackAndroidMidMileApp=.*|bStackAndroidMidMileApp=${{ env.TARGET_APP_ID }}|" \
                    -e "s|^bStackAndroidMidMileAppBuildNumber=.*|bStackAndroidMidMileAppBuildNumber=${{ env.TARGET_APP_BUILD_NUMBER }}|" \
                    resources/environments/browserStackConfigs.properties
                  ;;
                fleetApp)
                  sed -i'' \
                    -e "s|^bStackAndroidFleetApp=.*|bStackAndroidFleetApp=${{ env.TARGET_APP_ID }}|" \
                    -e "s|^bStackAndroidFleetAppBuildNumber=.*|bStackAndroidFleetAppBuildNumber=${{ env.TARGET_APP_BUILD_NUMBER }}|" \
                    resources/environments/browserStackConfigs.properties
                  ;;
              esac
              ;;
            ios)
              case "${{ env.TARGET_APP_NAME }}" in
                customerAppNative)
                  sed -i'' \
                    -e "s|^bStackIosCustomerAppNativeApp=.*|bStackIosCustomerAppNativeApp=${{ env.TARGET_APP_ID }}|" \
                    -e "s|^bStackIosCustomerAppNativeAppBuildNumber=.*|bStackIosCustomerAppNativeAppBuildNumber=${{ env.TARGET_APP_BUILD_NUMBER }}|" \
                    resources/environments/browserStackConfigs.properties
                  ;;
                customerAppReactNative)
                  sed -i'' \
                    -e "s|^bStackIosCustomerAppReactNativeApp=.*|bStackIosCustomerAppReactNativeApp=${{ env.TARGET_APP_ID }}|" \
                    -e "s|^bStackIosCustomerAppReactNativeAppBuildNumber=.*|bStackIosCustomerAppReactNativeAppBuildNumber=${{ env.TARGET_APP_BUILD_NUMBER }}|" \
                    resources/environments/browserStackConfigs.properties
                  ;;
                midMileApp)
                  sed -i'' \
                    -e "s|^bStackIosMidMileApp=.*|bStackIosMidMileApp=${{ env.TARGET_APP_ID }}|" \
                    -e "s|^bStackIosMidMileAppBuildNumber=.*|bStackIosMidMileAppBuildNumber=${{ env.TARGET_APP_BUILD_NUMBER }}|" \
                    resources/environments/browserStackConfigs.properties
                  ;;
                fleetApp)
                  sed -i'' \
                    -e "s|^bStackIosFleetApp=.*|bStackIosFleetApp=${{ env.TARGET_APP_ID }}|" \
                    -e "s|^bStackIosFleetAppBuildNumber=.*|bStackIosFleetAppBuildNumber=${{ env.TARGET_APP_BUILD_NUMBER }}|" \
                    resources/environments/browserStackConfigs.properties
                  ;;
              esac
              ;;
          esac

      - name: Display modified browserStack configs file contents
        run: |
          echo "Contents of browserStackConfigs.properties:"
          cat resources/environments/browserStackConfigs.properties

      - name: Install Appium
        run: npm install -g appium@latest

      - name: Install Appium drivers
        run: |
          appium driver install uiautomator2
          appium driver install xcuitest

      - name: Modify dbSshKey contents
        run: |
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > resources/environments/dbSshKey

      - name: Display modified card service config file contents
        run: |
          echo "Contents of cardServiceConfigs.properties:"
          cat resources/environments/cardServiceConfigs_${{ env.SELECTED_ENV }}.properties

      - name: Build with Maven and Execute Tests
        id: build_and_test
        run: |
          if [[ -n "${{ env.TARGET_TEST_GROUP }}" && "${{ env.TARGET_TEST_GROUP }}" != "undefined" ]]; then
            mvn test -Pfood-aggregator-tests -Dmaven.test.failure.ignore=true -Dbrowserstack.enabled=true -Dconnect.to.db=true -Denv=${{ env.SELECTED_ENV }} -Duse.order.api.v2=${{ env.USE_ORDER_API_V2 }} -Dgroups=${{ env.TARGET_TEST_GROUP }}
          elif [[ -n "${{ env.JIRA_TICKET_ID }}" && "${{ env.JIRA_TICKET_ID }}" != "B10-" ]]; then
            mvn test -Pfood-aggregator-tests -Dmaven.test.failure.ignore=true -Dbrowserstack.enabled=true -Dconnect.to.db=true -Denv=${{ env.SELECTED_ENV }} -Duse.order.api.v2=${{ env.USE_ORDER_API_V2 }} -Dgroups=${{ env.JIRA_TICKET_ID }}
          else
            mvn test -Pfood-aggregator-tests -Dmaven.test.failure.ignore=true -Dbrowserstack.enabled=true -Dconnect.to.db=true -Denv=${{ env.SELECTED_ENV }} -Duse.order.api.v2=${{ env.USE_ORDER_API_V2 }} -Dtestnames="${{ env.TARGET_TEST_NAME }}"
          fi

      - name: Generate Allure Reports
        if: always()
        run: mvn allure:report

      - name: Archive Allure Reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: allure-reports
          path: target/site/allure-maven-plugin

      - name: Archive TestNG Reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: testng-reports
          path: target/surefire-reports

      - name: Install libxml2-utils
        if: always()
        run: |
          sudo apt-get update
          sudo apt-get install -y libxml2-utils

      - name: Check Test Results
        if: always()
        id: check_test_results
        run: |
          TEST_RESULTS_FILE="target/surefire-reports/testng-results.xml"
          if [ -f "$TEST_RESULTS_FILE" ]; then
            EXECUTED_TESTS=$(xmllint --xpath "string(//testng-results/@total)" "$TEST_RESULTS_FILE")
            echo "executed_tests=$EXECUTED_TESTS" >> $GITHUB_ENV
          fi
          if [ "${{ steps.build_and_test.outcome }}" == "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
          fi

      - name: Output Executed Tests
        if: always()
        run: |
          echo "Executed Tests count: ${{ env.executed_tests }}"

      - name: Report Status Back to Triggering Repository
        if: always() && github.event_name == 'repository_dispatch'
        run: |
          BUILD_OUTCOME="${{ steps.build_and_test.outcome }}"
          EXECUTED_TESTS="${{ env.executed_tests }}"

          if [ "$BUILD_OUTCOME" == "success" ]; then
            STATE="success"
            if [ "$EXECUTED_TESTS" -eq 0 ]; then
              DESCRIPTION="0 Tests tied to the target Jira ticket."
            else
              DESCRIPTION="Food Aggregator Test passed successfully."
            fi
          elif [ "$BUILD_OUTCOME" == "failure" ]; then
            STATE="failure"
            DESCRIPTION="Food Aggregator Test failed."
          else
            STATE="error"
            DESCRIPTION="Food Aggregator Test encountered an error or was cancelled."
          fi

          TARGET_URL="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -s -X POST \
            -H "Authorization: Bearer ${{ secrets.GH_PAT }}" \
            -H "Accept: application/vnd.github+json" \
            -d '{
              "state": "'"$STATE"'",
              "target_url": "'"$TARGET_URL"'",
              "description": "'"$DESCRIPTION"'",
              "context": "Food Aggregator Tests Result"
            }' \
            https://api.github.com/repos/${{ env.TRIGGERING_REPO_OWNER }}/${{ env.TRIGGERING_REPO }}/statuses/${{ env.TRIGGERING_SHA_ID }}
      - name: Set Slack Channel ID
        if: always()
        run: |
            echo "TARGET_SLACK_CHANNEL_ID=${{ secrets.FOOD_AGGREGATOR_PIPELINE_SLACK_CHANNEL_ID }}" >> $GITHUB_ENV
        shell: bash
      - name: Post text to a Slack channel (Success)
        if: success()
        uses: slackapi/slack-github-action@v2.0.0
        with:
          method: chat.postMessage
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          payload: |
            channel: ${{ env.TARGET_SLACK_CHANNEL_ID }}
            text: "Food Aggregator tests passed. Download the reports here: <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}#artifacts|Download the reports> ${{ env.PR_LINK_TEXT }}"

      - name: Post text to a Slack channel (Failure)
        if: failure()
        uses: slackapi/slack-github-action@v2.0.0
        with:
          method: chat.postMessage
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          payload: |
            channel: ${{ env.TARGET_SLACK_CHANNEL_ID }}
            text: "ATTENTION: Food Aggregator tests failed. Download the reports here: <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}#artifacts|Download the reports> ${{ env.PR_LINK_TEXT }}"
