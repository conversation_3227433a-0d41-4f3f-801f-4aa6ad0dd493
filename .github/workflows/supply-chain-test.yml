name: Supply Chain Test

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select the target environment'
        required: true
        default: 'testing'
        type: choice
        options:
          - testing
          - integration
          - production
      test_country_code:
        description: 'Select the test country code'
        required: true
        default: 'EG'
        type: choice
        options:
          - EG
          - KSA
      target_test_name:
        description: 'Select the target test squad'
        required: true
        default: 'ops'
        type: choice
        options:
          - ops
          - logistics
          - inventory
          - supply-demand
      target_test_group:
        description: 'Type a group name for the tests. If you want the full tests in the test tag to run, leave empty'
        required: false
        default: ''
  repository_dispatch:
    types: [ supply-chain-test ]

jobs:
  supply-chain-test-check:
    name: Supply Chain Test - ${{ github.event.client_payload.environment || github.event.inputs.environment || 'testing' }} - Target Test name (${{ github.event.client_payload.target_test_name || github.event.inputs.target_test_name || 'ops' }}) - Target Test group (${{ github.event.client_payload.target_test_group || github.event.inputs.target_test_group || 'undefined' }})
    runs-on: ubuntu-latest
    environment: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'testing' }}
    env:
      SELECTED_ENV: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'testing' }}
      TEST_COUNTRY_CODE: ${{ github.event.client_payload.test_country_code || github.event.inputs.test_country_code || 'EG' }}
      TRIGGERING_REPO_OWNER: ${{ github.event.client_payload.triggering_repo_owner || 'undefined' }}
      TRIGGERING_REPO: ${{ github.event.client_payload.triggering_repo || 'undefined' }}
      TRIGGERING_SHA_ID: ${{ github.event.client_payload.sha_id || 'undefined' }}
      TARGET_TEST_NAME: ${{ github.event.client_payload.target_test_name || github.event.inputs.target_test_name || 'ops' }}
      TARGET_TEST_GROUP: ${{ github.event.client_payload.target_test_group || github.event.inputs.target_test_group || 'undefined' }}

    steps:
      - name: Validate Inputs and Values
        run: |
          # Validate presence of required fields for repository_dispatch
          if [ "${{ github.event_name }}" == "repository_dispatch" ]; then
            if [ -z "${{ github.event.client_payload.triggering_repo_owner }}" ] || \
               [ -z "${{ github.event.client_payload.triggering_repo }}" ] || \
               [ -z "${{ github.event.client_payload.sha_id }}" ]; then
              echo "Error: Required fields in client_payload are missing for repository_dispatch!"
              echo "Triggering Repo Owner: '${{ github.event.client_payload.triggering_repo_owner }}'"
              echo "Triggering Repo: '${{ github.event.client_payload.triggering_repo }}'"
              echo "Triggering SHA ID: '${{ github.event.client_payload.sha_id }}'"
              exit 1
            fi
          fi
          # Validate environment
          if [[ "${{ env.SELECTED_ENV }}" != "testing" && \
                "${{ env.SELECTED_ENV }}" != "integration" ]]; then
            echo "Error: Invalid environment '${{ env.SELECTED_ENV }}'. Must be one of: testing, integration"
            exit 1
          fi
          # Validate test_country_code
          if [[ "${{ env.TEST_COUNTRY_CODE }}" != "EG" && \
                "${{ env.TEST_COUNTRY_CODE }}" != "KSA" ]]; then
            echo "Error: Invalid test_country_code '${{ env.TEST_COUNTRY_CODE }}'. Must be one of: EG, KSA."
            exit 1
          fi
          # Validate TARGET_TEST_GROUP       
          if [[ "${{ env.TARGET_TEST_GROUP }}" != "ops" && \
                "${{ env.TARGET_TEST_GROUP }}" != "logistics" && \
                "${{ env.TARGET_TEST_GROUP }}" != "inventory" && \
                "${{ env.TARGET_TEST_GROUP }}" != "supply-demand" ]]; then
            echo "Error: Invalid target_test_group '${{ env.TARGET_TEST_GROUP }}'. Must be one of: ops, logistics, inventory, supply-demand."
            exit 1
          fi
          echo "All validations passed successfully!"
        shell: bash

      - name: Install jq
        run: sudo apt-get install -y jq

      - name: Fetch Pull Request Number
        if: github.event_name == 'repository_dispatch'
        id: fetch_pr_number
        run: |
          PR_RESPONSE=$(curl --location "https://api.github.com/repos/${{ env.TRIGGERING_REPO_OWNER }}/${{ env.TRIGGERING_REPO }}/commits/${{ env.TRIGGERING_SHA_ID }}/pulls" \
            --header "Accept: application/vnd.github+json" \
            --header "X-GitHub-Api-Version: 2022-11-28" \
            --header "Authorization: Bearer ${{ secrets.GH_PAT }}")

          PR_COUNT=$(echo "$PR_RESPONSE" | jq '. | length')

          if [ "$PR_COUNT" -gt 0 ]; then
            PR_NUMBER=$(echo "$PR_RESPONSE" | jq '.[0].number')
            echo "PR_NUMBER=$PR_NUMBER" >> $GITHUB_ENV
          else
            echo "No pull request found for the given commit SHA."
          fi

      - name: Form PR Link Text
        id: form_pr_link_text
        run: |
          if [ -n "${{ env.PR_NUMBER }}" ]; then
            PR_LINK="https://github.com/${{ env.TRIGGERING_REPO_OWNER }}/${{ env.TRIGGERING_REPO }}/pull/${{ env.PR_NUMBER }}"
            PR_LINK_TEXT="\nPR Link: <${PR_LINK}|View Pull Request>"
          else
            PR_LINK_TEXT=""
          fi
          echo "PR_LINK_TEXT=$PR_LINK_TEXT" >> $GITHUB_ENV

      - name: Create Pending Status Check
        if: github.event_name == 'repository_dispatch'
        run: |
          STATE="pending"
          DESCRIPTION="Supply Chain Test is in progress."
          TARGET_URL="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -s -X POST \
            -H "Authorization: Bearer ${{ secrets.GH_PAT }}" \
            -H "Accept: application/vnd.github+json" \
            -d '{
              "state": "'"$STATE"'",
              "target_url": "'"$TARGET_URL"'",
              "description": "'"$DESCRIPTION"'",
              "context": "Supply Chain Tests Result"
            }' \
            https://api.github.com/repos/${{ env.TRIGGERING_REPO_OWNER }}/${{ env.TRIGGERING_REPO }}/statuses/${{ env.TRIGGERING_SHA_ID }}

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 24
        uses: actions/setup-java@v4
        with:
          java-version: '24'
          distribution: 'oracle'
          cache: maven

      - name: Set up Maven
        uses: stCarolas/setup-maven@v5
        with:
          maven-version: 3.9.8

      - name: Install Node.js and npm
        uses: actions/setup-node@v4
        with:
          node-version: '20.18.0' # Specify the Node.js version you want to install
          registry-url: 'https://registry.npmjs.org/'

      - name: Copy config files
        run: |
          cp resources/environments/config.properties.example resources/environments/config_${{ env.SELECTED_ENV }}.properties
          cp resources/environments/webConfig.properties.example resources/environments/webConfig.properties
          cp resources/environments/cardServiceConfigs.properties.example resources/environments/cardServiceConfigs_${{ env.SELECTED_ENV }}.properties
          cp resources/environments/iPhone_14.properties.example resources/environments/iPhone_14.properties
          cp resources/environments/Pixel_2.properties.example resources/environments/Pixel_2.properties
          cp resources/environments/cardServiceEncryptionPublicKey.pub.example resources/environments/cardServiceEncryptionPublicKey.pub
          cp resources/environments/dbSshKey.example resources/environments/dbSshKey

      - name: Set Test Mobile Number
        id: set_mobile_number
        run: |
          if [ "$TEST_COUNTRY_CODE" == "EG" ]; then
            echo "test_mobile_number=+20" >> $GITHUB_ENV
          else
            echo "test_mobile_number=+966" >> $GITHUB_ENV
          fi

      - name: Select Test Mobile Company
        id: select_mobile_company
        run: |
          if [ "$TEST_COUNTRY_CODE" == "EG" ]; then
            echo "mobile_company=Etisalat" >> $GITHUB_ENV
          else
            echo "mobile_company=Zain" >> $GITHUB_ENV
          fi

      - name: Install OpenSSH client
        run: |
          sudo apt-get update
          sudo apt-get install -y openssh-client

      - name: Install MySQL client
        run: |
          sudo apt-get update
          sudo apt-get install -y mysql-client

      - name: Install Allure CLI
        run: |
          sudo apt-get update \
          && sudo apt-get install -y wget unzip \
          && wget -qO- https://github.com/allure-framework/allure2/releases/download/2.13.8/allure-2.13.8.zip -O /tmp/allure.zip \
          && sudo unzip -q /tmp/allure.zip -d /opt/ \
          && sudo ln -s /opt/allure-2.13.8/bin/allure /usr/bin/allure

      - name: Modify config.properties file
        run: |
          sed -i'' \
              -e "s/^testCountryCode=.*/testCountryCode=$TEST_COUNTRY_CODE/" \
              -e "s/^testMobileNumber=.*/testMobileNumber=${{ env.test_mobile_number }}/" \
              -e "s/^testMobileCompany=.*/testMobileCompany=${{ env.mobile_company }}/" \
              -e "s/^testFpName=.*/testFpName=${{ vars.FP_NAME }}/" \
              -e "s/^testFpDate=.*/testFpDate=${{ vars.FP_DATE }}/" \
              -e "s/^testLatitude=.*/testLatitude=${{ vars.WAREHOUSE_LATITUDE }}/" \
              -e "s/^testLongitude=.*/testLongitude=${{ vars.WAREHOUSE_LONGITUDE }}/" \
              -e "s|^testOrderInfo=.*|testOrderInfo=${{ vars.ORDER_INFO }}|" \
              -e "s/^adminBypassScriptPassword=.*/adminBypassScriptPassword=${{ secrets.ADMIN_BYPASS_SCRIPT_PASSWORD }}/" \
              -e "s/^adminGmailAddress=.*/adminGmailAddress=${{ secrets.ADMIN_GMAIL_ADDRESS }}/" \
              -e "s/^adminGmailPassword=.*/adminGmailPassword=${{ secrets.ADMIN_GMAIL_PASSWORD }}/" \
              -e "s/^midMilePassword=.*/midMilePassword=${{ secrets.MID_MILE_PASSWORD }}/" \
              -e "s/^ngrokAuthToken=.*/ngrokAuthToken=${{ secrets.NGROK_AUTH_TOKEN }}/" \
              -e "s/^pickerPassword=.*/pickerPassword=${{ secrets.PICKER_PASSWORD }}/" \
              -e "s/^slackApiToken=.*/slackApiToken=${{ secrets.SLACK_API_TOKEN }}/" \
              -e "s/^wpLoggedInCookieName=.*/wpLoggedInCookieName=${{ secrets.WP_LOGGED_IN_COOKIE_NAME }}/" \
              -e "s/^wpNodeAuthorizationCookieName=.*/wpNodeAuthorizationCookieName=${{ secrets.WP_NODE_AUTHORIZATION_COOKIE_NAME }}/" \
              -e "s/^wpSecCookieName=.*/wpSecCookieName=${{ secrets.WP_SEC_COOKIE_NAME }}/" \
              -e "s/^adminLocalPhoneNumber=.*/adminLocalPhoneNumber=${{ vars.ADMIN_LOCAL_PHONE_NUMBER }}/" \
              -e "s/^adminReferralCode=.*/adminReferralCode=${{ vars.ADMIN_REFERRAL_CODE }}/" \
              -e "s/^midMilePhoneNumber=.*/midMilePhoneNumber=${{ vars.MID_MILE_PHONE_NUMBER }}/" \
              -e "s/^pickerPhoneNumber=.*/pickerPhoneNumber=${{ vars.PICKER_PHONE_NUMBER }}/" \
              -e 's/^paymentServiceSecret=.*/paymentServiceSecret=${{ secrets.PAYMENT_SERVICE_SECRET }}/' \
              -e 's/^paymentShoppingKey=.*/paymentShoppingKey=${{ secrets.PAYMENT_SHOPPING_KEY }}/' \
              -e 's/^paymentTopUpKey=.*/paymentTopUpKey=${{ secrets.PAYMENT_TOPUP_KEY }}/' \
              -e 's/^paymentBillingKey=.*/paymentBillingKey=${{ secrets.PAYMENT_BILLING_KEY }}/' \
              -e 's/^paymentGratuityKey=.*/paymentGratuityKey=${{ secrets.PAYMENT_GRATUITY_KEY }}/' \
              -e "s/^mysqlUserPassword=.*/mysqlUserPassword=${{ secrets.MYSQL_USER_PASSWORD }}/" \
              -e "s/^sshPassphrase=.*/sshPassphrase=${{ secrets.SSH_KEY_PASSPHRASE }}/" \
              -e "s/^mysqlHost=.*/mysqlHost=${{ vars.MYSQL_HOST }}/" \
              -e "s/^mysqlUserName=.*/mysqlUserName=${{ vars.MYSQL_USERNAME }}/" \
              -e "s/^mysqlDatabaseName=.*/mysqlDatabaseName=${{ vars.MYSQL_DATABASE_NAME }}/" \
              -e "s/^mysqlServerPort=.*/mysqlServerPort=${{ vars.MYSQL_SERVER_PORT }}/" \
              -e "s/^sshConnectionRequired=.*/sshConnectionRequired=${{ vars.SSH_CONNECTION_REQUIRED }}/" \
              -e "s/^sshHost=.*/sshHost=${{ vars.SSH_HOST }}/" \
              -e "s/^sshUserName=.*/sshUserName=${{ vars.SSH_USERNAME }}/" \
              -e "s/^sshPort=.*/sshPort=${{ vars.SSH_PORT }}/" \
              -e "s/^isSshKeyProtected=.*/isSshKeyProtected=${{ vars.IS_SSH_KEY_PROTECTED }}/" \
              -e "s|^sshKeyPath=.*|sshKeyPath=${{ vars.SSH_KEY_PATH }}|" \
              -e "s|^baseURL=.*|baseURL=${{ vars.BASE_URL }}|" \
              -e "s|^controlRoomBaseURL=.*|controlRoomBaseURL=${{ vars.CONTORL_ROOM_BASE_URL }}|" \
              -e "s|^billingServicesBaseURL=.*|billingServicesBaseURL=${{ vars.BILLING_SERVICE_BASE_URL }}|" \
              -e "s|^midMileBaseURL=.*|midMileBaseURL=${{ vars.MID_MILE_BASE_URL }}|" \
              -e "s|^transitBaseURL=.*|transitBaseURL=${{ vars.TRANSIT_BASE_URL }}|" \
              -e "s|^cardServicesBaseURL=.*|cardServicesBaseURL=${{ vars.CARD_SERVICES_BASE_URL }}|" \
              -e "s|^cardServicesAdminPanelBaseURL=.*|cardServicesAdminPanelBaseURL=${{ vars.CARD_SERVICES_ADMIN_PANEL_BASE_URL }}|" \
              -e "s|^pickerServicesBaseURL=.*|pickerServicesBaseURL=${{ vars.PICKER_SERVICES_BASE_URL }}|" \
              -e "s|^logisticsBaseURL=.*|logisticsBaseURL=${{ vars.LOGISTICS_BASE_URL }}|" \
              -e "s|^fleetAppBaseURL=.*|fleetAppBaseURL=${{ vars.FLEET_APP_BASE_URL }}|" \
              -e "s|^fleetServiceBaseURL=.*|fleetServiceBaseURL=${{ vars.FLEET_SERVICE_BASE_URL }}|" \
              -e "s|^paymentServiceBaseURL=.*|paymentServiceBaseURL=${{ vars.PAYMENT_SERVICE_BASE_URL }}|" \
              -e "s|^internalOrdersBaseURL=.*|internalOrdersBaseURL=${{ vars.INTERNAL_ORDERS_BASE_URL }}|" \
              -e "s|^foodAggregatorServiceBaseURL=.*|foodAggregatorServiceBaseURL=${{ vars.FOOD_AGGREGATOR_BASE_URL }}|" \
              -e "s|^catalogBaseURL=.*|catalogBaseURL=${{ vars.CATALOG_BASE_URL }}|" \
              -e "s|^inaiBaseURL=.*|inaiBaseURL=${{ vars.INAI_BASE_URL }}|" \
              -e "s|^stockTakeBaseURL=.*|stockTakeBaseURL=${{ vars.STOCK_TAKE_BASE_URL }}|" \
              -e "s|^freshChatApiBaseURL=.*|freshChatApiBaseURL=${{ vars.FRESH_CHAT_API_BASE_URL }}|" \
              -e "s|^orderServiceBaseURL=.*|orderServiceBaseURL=${{ vars.ORDER_SERVICE_BASE_URL }}|" \
              -e "s|^dAPhoneNumber=.*|dAPhoneNumber=${{ vars.DA_PHONE_NUMBER }}|" \
              -e "s|^dAPhoneCountryCode=.*|dAPhoneCountryCode=${{ vars.DA_PHONE_COUNTRY_CODE }}|" \
              -e "s|^dAPassword=.*|dAPassword=${{ vars.DA_PASSWORD }}|" \
              -e "s|^fpManagerPhoneNumber=.*|fpManagerPhoneNumber=${{ vars.FP_MANAGER_PHONE_NUMBER }}|" \
              -e "s|^fpManagerPhoneCountryCode=.*|fpManagerPhoneCountryCode=${{ vars.FP_MANAGER_COUNTRY_CODE }}|" \
              -e "s|^fpManagerPassword=.*|fpManagerPassword=${{ vars.FP_MANAGER_PASSWORD }}|" \
              -e "s|^paymentPanelEmail=.*|paymentPanelEmail=${{ vars.PAYMENT_PANEL_EMAIL }}|" \
              -e "s|^paymentPanelPassword=.*|paymentPanelPassword=${{ secrets.PAYMENT_PANEL_PASSWORD }}|" \
              -e "s|^freshChatApiKey=.*|freshChatApiKey=${{ secrets.FRESH_CHAT_API_KEY }}|" \
              -e "s|^supplyChainStaticAuthToken=.*|supplyChainStaticAuthToken=${{ secrets.SUPPLY_CHAIN_STATIC_AUTH_TOKEN }}|" \
              -e "s|^freshChatChannelId=.*|freshChatChannelId=${{ vars.FRESH_CHAT_CHANNEL_ID }}|" \
              -e "s|^fallBackProductIds=.*|fallBackProductIds=${{ vars.FALLBACK_PRODUCT_IDS }}|" \
              -e "s|^targetQtyForEachFallBackProduct=.*|targetQtyForEachFallBackProduct=${{ vars.TARGET_QTY_FOR_EACH_FALLBACK_PRODUCT }}|" \
              -e "s|^excludedCategoryIds=.*|excludedCategoryIds=${{ vars.EXCLUDED_CATEGORY_IDS }}|" \
          resources/environments/config_${{ env.SELECTED_ENV }}.properties

      - name: Display modified config file contents
        run: |
          echo "Contents of config.properties:"
          cat resources/environments/config_${{ env.SELECTED_ENV }}.properties

      - name: Modify cardServiceConfigs.properties
        run: |
          sed -i'' \
            -e "s|^adminUserName=.*|adminUserName=${{ vars.TESTING_CARDSERVICE_ADMIN_USERNAME }}|" \
            -e "s|^adminPassword=.*|adminPassword=${{ secrets.TESTING_CARDSERVICE_ADMIN_PASSWORD }}|" \
            -e "s|^loginMobileSchemeUserName=.*|loginMobileSchemeUserName=${{ vars.TESTING_CARDSERVICE_LOGIN_MOBILE_SCHEME_USER_NAME }}|" \
            -e "s|^loginMobileSchemePassword=.*|loginMobileSchemePassword=${{ secrets.TESTING_CARDSERVICE_LOGIN_MOBILE_SCHEME_PASSWORD }}|" \
            -e "s|^defaultCardPasscode=.*|defaultCardPasscode=${{ secrets.CARDSERVICE_DEFAULT_PASSCODE }}|" \
            -e "s|^cardUserBackTransactionId=.*|cardUserBackTransactionId=${{ vars.CARDSERVICE_CARD_USER_ID_BACK_TRANSACTION_ID }}|" \
            -e "s|^cardUserFrontTransactionId=.*|cardUserFrontTransactionId=${{ vars.CARDSERVICE_CARD_USER_ID_FRONT_TRANSACTION_ID }}|" \
            -e "s|^cardServiceContractNumber=.*|cardServiceContractNumber=${{ vars.CARDSERVICE_CONTRACT_NUMBER }}|" \
            -e "s|^cardServiceProductNumber=.*|cardServiceProductNumber=${{ vars.CARDSERVICE_PRODUCT_NUMBER }}|" \
            -e "s|^cardServiceTypeId=.*|cardServiceTypeId=${{ vars.CARDSERVICE_TYPE_ID }}|" \
          resources/environments/cardServiceConfigs_${{ env.SELECTED_ENV }}.properties

      - name: Modify cardServiceEncryptionPublicKey.pub contents
        run: |
          echo "${{ secrets.TESTING_CARDSERVICE_MOBILE_USER_PUBLIC_KEY }}" > resources/environments/cardServiceEncryptionPublicKey.pub

      - name: Modify dbSshKey contents
        run: |
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > resources/environments/dbSshKey

      - name: Display modified card service config file contents
        run: |
          echo "Contents of cardServiceConfigs.properties:"
          cat resources/environments/cardServiceConfigs_${{ env.SELECTED_ENV }}.properties

      - name: Build with Maven and Execute Tests
        id: build_and_test
        run: |
          if [[ -n "${{ env.TARGET_TEST_GROUP }}" && "${{ env.TARGET_TEST_GROUP }}" != "undefined" ]]; then
            mvn test -Psupplychain-tests -Dmaven.test.failure.ignore=true -Dconnect.to.db=true -Denv=${{ env.SELECTED_ENV }} -Dtestnames=${{ env.TARGET_TEST_NAME }} -Dgroups=${{ env.TARGET_TEST_GROUP }}
          else
            mvn test -Psupplychain-tests -Dmaven.test.failure.ignore=true -Dconnect.to.db=true -Denv=${{ env.SELECTED_ENV }} -Dtestnames=${{ env.TARGET_TEST_NAME }}
          fi

      - name: Generate Allure Reports
        if: always()
        run: allure generate allure-results --clean

      - name: Archive Allure Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: allure-report
          path: allure-report

      - name: Upload screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: screenshots
          path: resources/screenshots/**
          if-no-files-found: ignore
          retention-days: 14

      - name: Archive Allure Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: allure-results
          path: allure-results

      - name: Archive TestNG Reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: testng-reports
          path: target/surefire-reports

      - name: Zip the Allure Test Results directory
        if: always()
        run: zip -r allure-results.zip allure-results

      - name: Upload Allure Test Results to Browserstack
        if: always()
        run: |
          curl -u "${{ secrets.BROWSERSTACK_TEST_OBSERVABILITY_KEY }}" -vvv \
          -X POST \
          -F "data=@allure-results.zip" \
          -F "projectName=Supply-chain" \
          -F "buildName=Default Supply-chain Build" \
          -F "tags=Smoke" \
          -F "format=allure" \
          https://upload-observability.browserstack.com/upload

      - name: Check Test Results
        if: always()
        id: check_test_results
        run: |
          if [ "${{ steps.build_and_test.outcome }}" == "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
          fi

      - name: Report Status Back to Triggering Repository
        if: always() && github.event_name == 'repository_dispatch'
        run: |
          BUILD_OUTCOME="${{ steps.build_and_test.outcome }}"

          if [ "$BUILD_OUTCOME" == "success" ]; then
            STATE="success"
            DESCRIPTION="Supply Chain Test passed successfully."
          elif [ "$BUILD_OUTCOME" == "failure" ]; then
            STATE="failure"
            DESCRIPTION="Supply Chain Test failed."
          else
            STATE="error"
            DESCRIPTION="Supply Chain Test encountered an error or was cancelled."
          fi

          TARGET_URL="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -s -X POST \
            -H "Authorization: Bearer ${{ secrets.GH_PAT }}" \
            -H "Accept: application/vnd.github+json" \
            -d '{
              "state": "'"$STATE"'",
              "target_url": "'"$TARGET_URL"'",
              "description": "'"$DESCRIPTION"'",
              "context": "Supply Chain Tests Result"
            }' \
            https://api.github.com/repos/${{ env.TRIGGERING_REPO_OWNER }}/${{ env.TRIGGERING_REPO }}/statuses/${{ env.TRIGGERING_SHA_ID }}

      - name: Set Slack Channel ID
        if: always()
        run: |
          if [ "${{ env.TARGET_TEST_GROUP }}" = "ops" ]; then
            echo "TARGET_SLACK_CHANNEL_ID=${{ secrets.OPS_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID }}" >> $GITHUB_ENV
          elif [ "${{ env.TARGET_TEST_GROUP }}" == "logistics" ]; then 
            echo "TARGET_SLACK_CHANNEL_ID=${{ secrets.LOGISTICS_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID }}" >> $GITHUB_ENV
          elif [ "${{ env.TARGET_TEST_GROUP }}" == "inventory" ]; then  
          echo "TARGET_SLACK_CHANNEL_ID=${{ secrets.INVENTORY_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID }}" >> $GITHUB_ENV
          elif [ "${{ env.TARGET_TEST_GROUP }}" == "supply-demand" ]; then
          echo "TARGET_SLACK_CHANNEL_ID=${{ secrets.SUPPLY_DEMAND_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID }}" >> $GITHUB_ENV
          else
            echo "Error: Invalid target_group_runner '${{ env.TARGET_TEST_GROUP }}'. Must be one of: ops, logistics, inventory, supply-demand"
            exit 1
          fi
        shell: bash

      - name: Post text to a Slack channel (Success)
        if: success()
        uses: slackapi/slack-github-action@v2.0.0
        with:
          method: chat.postMessage
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          payload: |
            channel: ${{ env.TARGET_SLACK_CHANNEL_ID }}
            text: "Tests passed. Download the reports here: <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}#artifacts|Download the reports> ${{ env.PR_LINK_TEXT }}"

      - name: Post text to a Slack channel (Failure)
        if: failure()
        uses: slackapi/slack-github-action@v2.0.0
        with:
          method: chat.postMessage
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          payload: |
            channel: ${{ env.TARGET_SLACK_CHANNEL_ID }}
            text: "ATTENTION: Tests failed. Download the reports here: <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}#artifacts|Download the reports> ${{ env.PR_LINK_TEXT }}"
